import re
import json
import asyncio
from typing import Any, Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from controllers.kontratar.ebuy_opps_controller import EBUYOppsController
from controllers.kontratar.opps_table_controller import OppsTableController
from services.chroma.chroma_service import ChromaService
from services.proposal.key_personnel import KeyPersonnelService
from services.proposal.research_enhancement_service import ResearchEnhancementService

from database import get_customer_db, get_kontratar_db
from services.llm.llm_factory import get_llm
from services.proposal.utilities import ProposalUtilities
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type

from loguru import logger



def remove_first_markdown_title_regex(text: str) -> str:
    """
    Remove only the first line that starts with ## using regex.
    
    Args:
        text: Input text containing markdown titles
        
    Returns:
        Text with only the first ## title removed
    """
    # Remove only the first line starting with ## (with optional whitespace before ##)
    return re.sub(r'^\s*##.*$', '', text, flags=re.MULTILINE, count=1).strip()


class ProposalOutlineService:
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.sam_service = OppsTableController()
        self.ebuy_service = EBUYOppsController()
        self.custom_service = CustomOpportunitiesController()

        self.chroma_service = ChromaService(embedding_api_url, None)
        self.key_personnel_service = KeyPersonnelService()
        self.research_enhancement_service = ResearchEnhancementService()
        # Use the LLM factory to get the configured LLM
        self.llm = get_llm(
            temperature=0,
            num_ctx=6300,
            base_url=llm_api_url  # For backward compatibility with Ollama
        )

    async def generate_chroma_query(self, text: str, is_rfp: bool = True):
        if not text:
            return ""

        # Use the same LLM instance as the main service
        llm = self.llm
        if is_rfp:
            prompt = (
                "Given the following RFP volume information, generate a concise search query that would retrieve the most relevant government opportunity information from a vector database collection. "
                "The query should focus on requirements, evaluation criteria, and any details relevant to the specific volume described below. "
                "This is for a single volume of a multi-volume RFP.\n\n"
                f"{text}\n\n"
                "Search Query:"
            )
        else:
            prompt = (
                "Given the following RFI information, generate a concise search query that would retrieve the most relevant government opportunity information from a vector database collection. "
                "The query should focus on requirements, government needs, and any details relevant to the RFI topics described below.\n\n"
                f"{text}\n\n"
                "Search Query:"
            )
        try:
            messages = [("human", prompt)]
            response = await asyncio.wait_for(
                asyncio.to_thread(llm.invoke, messages),
                timeout=120  # 2 minute timeout
            )
            return str(response.content)
        except asyncio.TimeoutError:
            logger.error("LLM invocation timed out for search query generation")
            raise Exception("LLM request timed out")
        except Exception as e:
            logger.error(f"LLM invocation failed for search query generation: {e}")
            raise

    async def get_opportunity(self, opportunity_id: str, tenant_id: str, source: str):
        logger.info(f"get_opportunity called with opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        record = None
        if source == "sam":
            logger.info(f"Searching SAM for notice_id={opportunity_id}")
            async for db in get_kontratar_db():
                record = await self.sam_service.get_by_notice_id(db, opportunity_id)
                logger.info(f"SAM search result: {record}")
                break
        elif source == "ebuy":
            logger.info(f"Searching EBUY for rfq_id={opportunity_id}")
            async for db in get_kontratar_db():
                record = await self.ebuy_service.get_by_rfq_id(db, opportunity_id)
                logger.info(f"EBUY search result: {record}")
                break
        elif source == "custom":
            logger.info(f"Searching CUSTOM for opportunity_id={opportunity_id}")
            async for db in get_customer_db():
                record = await self.custom_service.get_main_info_by_opportunity_id(db, opportunity_id)
                logger.info(f"CUSTOM search result: {record}")
                break
        else:
            logger.error(f"Invalid source type: {source}")
            raise ValueError("Invalid source type")

        if record is None:
            logger.error(f"Error getting opportunity metadata for id={opportunity_id}, source={source}")
            raise ValueError("Error getting opportunity metadata")

        logger.info(f"Returning opportunity record: {record}")
        return record        

    async def generate_table_of_contents(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        volume_information: str,
        content_compliance: str,
        is_rfp: bool
    ) -> Dict[str, Any]:
        """
        Generate a comprehensive table of contents for government proposals.

        Enhanced for government compliance with:
        - Comprehensive context retrieval from ChromaDB
        - Government-specific prompt engineering
        - Detailed validation and error handling
        - Structured logging for debugging
        """

        logger.info(f"TOC: Starting table of contents generation for opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        logger.info(f"TOC: Processing {'RFP' if is_rfp else 'RFI'} volume")

        # Enhanced context retrieval with multiple targeted queries
        context = ""
        try:
            chroma_query = await self.generate_chroma_query(volume_information, is_rfp)
            logger.debug(f"TOC: Generated ChromaDB query: {chroma_query[:100]}...")

            async for db in get_kontratar_db():
                collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
                logger.debug(f"TOC: Using ChromaDB collection: {collection_name}")

                # Multiple queries for comprehensive context
                queries = [
                    chroma_query,
                    f"Table of contents structure and organization requirements for {'RFP' if is_rfp else 'RFI'}",
                    f"Section requirements and evaluation criteria for proposal volumes",
                    f"Statement of work tasks and technical requirements"
                ]

                all_chunks = []
                for i, query in enumerate(queries, 1):
                    try:
                        chunks = await asyncio.wait_for(
                            self.chroma_service.get_relevant_chunks(db, collection_name, query, n_results=3),
                            timeout=30.0
                        )
                        all_chunks.extend(chunks)
                        logger.debug(f"TOC: Query {i}/{len(queries)} retrieved {len(chunks)} chunks")
                    except asyncio.TimeoutError:
                        logger.warning(f"TOC: ChromaDB timeout for query {i}: {query[:50]}...")
                    except Exception as e:
                        logger.error(f"TOC: ChromaDB error for query {i}: {e}")

                if all_chunks:
                    # Clean up and deduplicate chunks
                    toc_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in all_chunks]
                    context = "\n".join(list(dict.fromkeys(toc_context)))  # Remove duplicates while preserving order
                    logger.info(f"TOC: Retrieved {len(all_chunks)} total chunks, {len(context)} characters of context")
                else:
                    logger.warning(f"TOC: No context retrieved from ChromaDB")
                break

        except Exception as e:
            logger.error(f"TOC: Error during context retrieval: {e}")
            context = ""

        system_prompt = '''
            **ROLE:** Senior Government Proposal Table of Contents Expert
            **MISSION:** Generate comprehensive, government-compliant table of contents that ensure complete coverage of all solicitation requirements and evaluation criteria.

            **CRITICAL GOVERNMENT COMPLIANCE REQUIREMENTS:**
            1. ABSOLUTE PRECISION: Every section must directly address specific solicitation requirements
            2. ZERO OMISSIONS: All Statement of Work (SOW) tasks must be included with exact naming
            3. COMPLETE COVERAGE: All evaluation factors and criteria must be addressed
            4. PROPER STRUCTURE: Follow exact government proposal formatting standards
            5. NO HALLUCINATION: Use only information explicitly provided in the context
            6. TRACEABILITY: Every section must be traceable to specific requirements

            **TABLE OF CONTENTS STANDARDS:**
            - Use professional government proposal terminology
            - Maintain logical flow and hierarchy
            - Ensure comprehensive coverage without redundancy
            - Follow federal proposal evaluation standards
            - Include all mandatory sections as specified in solicitation
            - Organize content for optimal evaluator review

            **SECTION ORGANIZATION PRINCIPLES:**
            - Main sections (1.0, 2.0, 3.0) for primary evaluation areas
            - Subsections (1.1, 1.2, 1.3) for detailed requirements
            - Sub-subsections (1.1.1, 1.1.2) only when necessary for complex requirements
            - Maximum 3 levels of hierarchy for clarity
            - Logical progression from overview to detailed implementation

            **CONTENT DESCRIPTION STANDARDS:**
            - Each section description must specify EXACTLY what content is required
            - Reference specific evaluation criteria and requirements
            - Indicate required deliverables, methodologies, or demonstrations
            - Specify compliance requirements and standards to address
            - Include any mandatory formats, templates, or structures

            **JSON OUTPUT REQUIREMENTS:**
            - Generate ONLY valid JSON - no additional text or formatting
            - Follow the exact schema provided without deviation
            - Ensure all string fields are properly escaped
            - Validate section numbering follows proper hierarchy
            - Maintain consistent formatting and structure
        '''

        user_prompt = f'''
            **SOLICITATION TYPE:** {'RFP' if is_rfp else 'RFI'}

            **STRUCTURE REQUIREMENTS:**
            <structure-compliance>
                {volume_information}
            </structure-compliance>

            **CONTENT REQUIREMENTS:**
            <content-compliance>
                {content_compliance}
            </content-compliance>

            **ADDITIONAL CONTEXT:**
            <context>
                {context[:2000] if context else "No additional context available"}
            </context>

            **GENERATION DIRECTIVE:**
            Create a comprehensive table of contents that ensures complete compliance with all solicitation requirements. Every section must directly address specific evaluation criteria and requirements.

            **CRITICAL REQUIREMENTS:**
            1. **STRUCTURE COMPLIANCE:** Follow the exact volume structure and naming conventions specified in <structure-compliance>
            2. **CONTENT COVERAGE:** Address every requirement listed in <content-compliance>
            3. **SOW INTEGRATION:** Include ALL Statement of Work tasks in the Technical Approach section with exact naming
            4. **PAST PERFORMANCE:** Include exactly 3 relevant project experiences under Past Performance/Demonstrated Experience
            5. **EVALUATION ALIGNMENT:** Organize sections to match evaluation criteria and factors
            6. **GOVERNMENT STANDARDS:** Use professional government proposal terminology and structure
            7. **PAGE LIMITS:** Extract exact page limits for each section from structure compliance data

            **PAGE LIMIT EXTRACTION INSTRUCTIONS:**
            - Look in the structure compliance data for sections with "section_name" and "page_limit"
            - Extract the exact "page_limit" value for each section
            - If a section is not found in structure compliance, use 2 as default
            - EVERY section and subsection MUST have a page_limit field

            **MANDATORY JSON SCHEMA:**
            {{
                "table_of_contents": [
                    {{
                        "title": "string",
                        "description": "string",
                        "number": "string",
                        "page_limit": number - extract exact page limit from structure compliance data,
                        "subsections": [
                            {{
                                "number": "string",
                                "title": "string",
                                "description": "string",
                                "page_limit": number - extract exact page limit from structure compliance data
                            }}
                        ]
                    }}
                ]
            }}

            **FIELD SPECIFICATIONS:**
            - "table_of_contents": Array of main sections for the {'RFP volume' if is_rfp else 'RFI response'}
            - "title": Section name (MAX 65 characters) - must align with <structure-compliance> naming conventions
              * Use clear, professional language without abbreviations
              * Examples: "Technical Approach" not "Tech App", "Past Performance" not "Past Perf"
            - "description": Detailed description of required content (minimum 50 characters)
              * Specify EXACTLY what must be included in this section
              * Reference specific evaluation criteria or requirements
              * Include required deliverables, methodologies, or compliance items
            - "number": Section numbering starting from 1.0 (e.g., 1.0, 1.1, 1.1.1)
              * Main sections: 1.0, 2.0, 3.0, etc.
              * Subsections: 1.1, 1.2, 1.3, etc.
              * Sub-subsections: 1.1.1, 1.1.2, etc. (use sparingly)
            - "page_limit": Extract exact page limit for this section from structure compliance data
              * MUST match the page limits specified in the structure compliance
              * Use 2 as default only if not found in structure compliance
            - "subsections": Array of subsections (use only when necessary for organization)

            **VALIDATION REQUIREMENTS:**
            - Use ONLY information from the provided context sections
            - Ensure mathematical accuracy in section numbering
            - Verify all SOW tasks are included in appropriate sections
            - Confirm all evaluation factors are addressed
            - Maintain logical flow and hierarchy
            - Generate valid JSON only - no additional text
        '''

        # Enhanced LLM call with comprehensive error handling and validation
        logger.info("TOC: Invoking LLM for table of contents generation")

        max_attempts = 3
        content = None

        for attempt in range(max_attempts):
            try:
                logger.debug(f"TOC: LLM attempt {attempt + 1}/{max_attempts}")

                messages = [
                    ("system", system_prompt),
                    ("human", user_prompt)
                ]

                result = await asyncio.wait_for(
                    asyncio.to_thread(self.llm.invoke, messages),
                    timeout=300  # 5 minute timeout
                )

                if result and result.content:
                    content = result.content.strip()
                    logger.info(f"TOC: LLM invocation successful on attempt {attempt + 1}")
                    break
                else:
                    logger.warning(f"TOC: Empty LLM response on attempt {attempt + 1}")
                    if attempt == max_attempts - 1:
                        raise Exception("LLM returned empty content after all attempts")

            except asyncio.TimeoutError:
                logger.error(f"TOC: LLM invocation timed out on attempt {attempt + 1}")
                if attempt == max_attempts - 1:
                    raise Exception("LLM request timed out after all attempts")
                await asyncio.sleep(2)  # Brief delay before retry

            except Exception as e:
                logger.error(f"TOC: LLM invocation failed on attempt {attempt + 1}: {e}")
                if attempt == max_attempts - 1:
                    raise Exception(f"LLM invocation failed after all attempts: {e}")
                await asyncio.sleep(2)  # Brief delay before retry

        if content is None:
            raise Exception("Failed to generate table of contents after all attempts")

        # Clean and validate JSON structure
        try:
            # Clean the content - remove markdown code blocks if present
            cleaned_content = content.strip()
            if cleaned_content.startswith("```json"):
                cleaned_content = cleaned_content.replace("```json", "").replace("```", "").strip()
            elif cleaned_content.startswith("```"):
                cleaned_content = cleaned_content.replace("```", "").strip()

            # Try to parse the cleaned content
            parsed_content = json.loads(cleaned_content)

            # Update content with cleaned version
            content = cleaned_content
            if "table_of_contents" not in parsed_content:
                logger.warning("TOC: Generated content missing 'table_of_contents' key")
            else:
                toc_sections = parsed_content["table_of_contents"]
                logger.info(f"TOC: Successfully generated {len(toc_sections)} main sections")

                # Log section summary for debugging
                for i, section in enumerate(toc_sections[:5], 1):  # Show first 5 sections
                    section_title = section.get("title", "Unknown")
                    section_number = section.get("number", "Unknown")
                    subsection_count = len(section.get("subsections", []))
                    logger.debug(f"TOC: Section {i}: {section_number} - {section_title} ({subsection_count} subsections)")

        except json.JSONDecodeError as e:
            logger.error(f"TOC: Generated content is not valid JSON: {e}")
            logger.debug(f"TOC: Raw content: {content[:500]}...")
            # Don't raise here - let the calling code handle JSON parsing

        logger.info("TOC: Table of contents generation completed successfully")
        logger.debug(f"TOC: Generated content length: {len(content)} characters")

        return {"content": content}

    
    async def generate_outline(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        table_of_contents: List[Dict[str, Any]],
        is_rfp: bool = True
    ) -> Dict[str, Any]:
        """
        Generate a detailed outline for each section and subsection in the table of contents.
        Each outline contains a title, content, and optionally an array of image descriptions.
        The outlines are nested to match the table of contents hierarchy.

        Enhanced for government standards with:
        - Direct compliance data integration for better accuracy
        - Comprehensive validation and error handling
        - Government-compliant prompt engineering
        - Structured context retrieval with compliance context
        - Quality assurance checks

        Args:
            opportunity_id: Unique identifier for the opportunity
            tenant_id: Tenant identifier
            source: Data source ("custom" or "sam")
            table_of_contents: Generated table of contents structure
            content_compliance: Content compliance requirements from ContentComplianceService
            structure_compliance: Structure compliance data from StructureComplianceService
            is_rfp: Whether this is an RFP (True) or RFI (False)
        """

        logger.info(f"OUTLINE: Starting enhanced outline generation for opportunity {opportunity_id}")
        logger.info(f"OUTLINE: Processing {len(table_of_contents)} main sections for {'RFP' if is_rfp else 'RFI'}")
        logger.info(f"OUTLINE: Using table of contents with page limits for all sections")

        @retry(
            stop=stop_after_attempt(5),
            wait=wait_fixed(3),
            retry=retry_if_exception_type(Exception),
            reraise=True
        )
        async def outline_for_section(section: Dict[str, Any], depth: int = 0) -> Dict[str, Any]:
            """Enhanced section outline generation with government compliance"""
            section_title = section.get("title", "").strip()
            section_desc = section.get("description", "").strip()
            section_number = section.get("number", "").strip()

            indent = "  " * depth
            logger.info(f"OUTLINE: {indent}Processing section {section_number} - {section_title}")

            if not section_title:
                logger.warning(f"OUTLINE: {indent}Empty section title found, skipping")
                return {}

            # Enhanced ChromaDB query strategy
            chroma_queries = [
                f"Requirements and evaluation criteria for {section_title} section",
                f"Content specifications and deliverables for {section_title}",
                f"Government standards and compliance requirements for {section_title}",
                f"Technical approach and methodology requirements for {section_title}"
            ]

            # Fetch comprehensive context with timeout protection
            context_chunks = []
            try:
                async for db in get_kontratar_db():
                    collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id

                    for query in chroma_queries:
                        try:
                            chunks = await asyncio.wait_for(
                                self.chroma_service.get_relevant_chunks(db, collection_name, query, n_results=2),
                                timeout=30.0
                            )
                            context_chunks.extend(chunks)
                        except asyncio.TimeoutError:
                            logger.warning(f"OUTLINE: {indent}ChromaDB timeout for query: {query[:50]}...")
                        except Exception as e:
                            logger.error(f"OUTLINE: {indent}ChromaDB error for query: {e}")
                    break

            except Exception as e:
                logger.error(f"OUTLINE: {indent}Database connection error: {e}")

            # Process and clean context
            if context_chunks:
                section_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in context_chunks]
                context = "\n".join(section_context)
                logger.info(f"OUTLINE: {indent}Retrieved {len(context_chunks)} context chunks ({len(context)} chars)")
            else:
                context = f"Section: {section_title}\nDescription: {section_desc}"
                logger.warning(f"OUTLINE: {indent}No context retrieved, using fallback")

            # Enhanced government-compliant system prompt with compliance integration
            system_prompt = '''
                **ROLE:** Senior Government Proposal Outline Expert
                **MISSION:** Generate comprehensive, government-compliant proposal section outlines that meet federal evaluation standards using provided compliance requirements.

                **CRITICAL GOVERNMENT COMPLIANCE REQUIREMENTS:**
                1. ZERO placeholders, brackets, TBD, TODO, or incomplete information in any field
                2. NO generic content - everything must be specific to THIS section and requirement
                3. NO repetition of RFP administrative requirements or formatting rules
                4. FOCUS EXCLUSIVELY on demonstrating technical capability for the specific requirement
                5. PROVIDE concrete, actionable guidance that leads to substantive content
                6. ENSURE all guidance aligns with federal proposal evaluation criteria
                7. MANDATE specific methodologies, processes, and measurable outcomes
                8. INTEGRATE content compliance requirements to ensure all mandatory elements are addressed
                9. RESPECT structure compliance data for proper volume organization and page limits
                10. ALIGN with Statement of Work (SOW) tasks and evaluation factors

                **CRITICAL REQUIREMENTS:**
                1. **EXACT TITLE**: Use the EXACT section title from the table of contents: "{section_title}"
                2. **EXACT PAGE LIMIT**: MUST be EXACTLY {section.get("page_limit", 2)} pages (from table of contents) - NO CHANGES ALLOWED
                3. **EXACT DESCRIPTION**: Follow the section description from table of contents: "{section_desc}"
                4. **NO HALLUCINATION**: Only use information from the provided RFP context and table of contents
                5. **GOVERNMENT STANDARD**: Use professional government proposal terminology

                **OUTLINE COMPONENTS (ALL REQUIRED):**
                - title: MUST BE EXACTLY "{section_title}" (no variations, no changes)
                - content: Comprehensive guidance on what to include and what to avoid
                - page_limit: MUST BE EXACTLY {section.get("page_limit", 2)} pages (from table of contents)
                - purpose: Primary evaluation purpose (Demonstrate Capability, Show Understanding, Prove Experience, etc.)
                - rfp_vector_db_query: Specific query to retrieve RFP requirements for this section
                - client_vector_db_query: Targeted query to get relevant company capabilities
                - custom_prompt: Detailed, step-by-step content generation instructions
                - references: Exact text from context that supports this outline
                - image_descriptions: Required tables/diagrams (if applicable)

                **CONTENT GUIDANCE STANDARDS:**
                - Specify EXACTLY what technical details to include based on RFP requirements
                - Define SPECIFIC methodologies and processes to describe per Statement of Work tasks
                - Identify MEASURABLE outcomes and success criteria to present from evaluation factors
                - Clarify HOW to demonstrate understanding of government requirements using RFP context
                - Outline CONCRETE examples and case studies to reference that align with past performance criteria
                - Specify REQUIREMENTS and standards to address from RFP documentation
                - USE page limits from table of contents ({section.get("page_limit", 2)} pages) for accurate allocation
                - ALIGN section content with proposal volume organization requirements
                - INTEGRATE evaluation criteria and factors from RFP requirements

                **CUSTOM PROMPT REQUIREMENTS:**
                - Include step-by-step content creation instructions
                - Specify required technical depth and detail level
                - Mandate specific government terminology and naming conventions
                - Define required structure (headers, bullets, tables)
                - Include quality checkpoints and validation criteria
                - Specify word count and page limit compliance

                **MANDATORY TABLE/DIAGRAM IDENTIFICATION:**
                - Staffing Plan: Role/Responsibilities/Qualifications table
                - Technical Approach: Process flow diagrams, methodology tables
                - Past Performance: Project summary tables
                - Management Plan: Organizational charts, timeline tables
                - Quality Assurance: QA process diagrams, metrics tables

                **JSON COMPLIANCE:**
                - Return ONLY valid JSON - no explanatory text
                - Follow the exact schema provided
                - Ensure all string fields are properly escaped
                - Validate all required fields are present
            '''

            # Enhanced user prompt with structured context from table of contents
            user_prompt = f'''
                **SECTION ANALYSIS:**
                Section Number: {section_number}
                Section Title: {section_title}
                Section Description: {section_desc}
                Page Limit: {section.get("page_limit", 2)} pages

                **RFP CONTEXT:**
                {context[:2000] if context else "No specific RFP context available"}

                **REQUIREMENTS:**
                Generate a comprehensive outline that enables creation of government-compliant content.
                Use the content compliance requirements to ensure all mandatory elements are addressed.
                Use the structure compliance data to understand volume organization and page limits.
                Focus on the specific requirements and evaluation criteria for this section.

                **MANDATORY JSON SCHEMA:**
                {{
                    "title": "{section_title}",
                    "content": "string - detailed guidance based on table of contents description (minimum 200 words)",
                    "page_limit": {section.get("page_limit", 2)},
                    "purpose": "string - primary evaluation purpose",
                    "rfp_vector_db_query": "string - specific query for RFP requirements",
                    "client_vector_db_query": "string - targeted query for company capabilities",
                    "custom_prompt": "string - step-by-step content generation instructions for EXACTLY {section.get("page_limit", 2)} pages",
                    "references": "string - exact text from context supporting this outline",
                    "image_descriptions": ["string"] - required tables/diagrams (if applicable)
                }}

                **STRICT VALIDATION RULES:**
                - title MUST be exactly "{section_title}"
                - page_limit MUST be exactly {section.get("page_limit", 2)}
                - content MUST align with table of contents description: "{section_desc}"

                **PAGE LIMIT REQUIREMENTS:**
                This section has a page limit of {section.get("page_limit", 2)} pages as specified in the table of contents
                Content must be designed to fit within this limit when generated

                **VALIDATION REQUIREMENTS:**
                - Title must be EXACTLY "{section_title}" (no variations, no changes)
                - Page limit must be EXACTLY {section.get("page_limit", 2)} (from table of contents - NO CHANGES)
                - Content must align with TOC description: "{section_desc}"
                - Custom prompt must specify EXACTLY {section.get("page_limit", 2)} pages
                - All fields must be complete and substantive
                - No placeholders or generic content
                - Custom prompt must include specific government terminology
                - Content guidance must be actionable and specific
                - References must be exact quotes from provided RFP context

                **FAILURE TO FOLLOW TABLE OF CONTENTS EXACTLY WILL RESULT IN REJECTION**
            '''

            # Enhanced LLM call with comprehensive validation
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    logger.info(f"OUTLINE: {indent}LLM attempt {attempt + 1}/{max_attempts} for {section_title}")

                    messages = [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ]
                    try:
                        result = await asyncio.wait_for(
                            asyncio.to_thread(self.llm.invoke, messages),
                            timeout=300  # 5 minute timeout
                        )
                        content = str(result.content).strip()
                    except asyncio.TimeoutError:
                        logger.error(f"OUTLINE: {indent}LLM timeout on attempt {attempt + 1} for {section_title}")
                        if attempt == max_attempts - 1:
                            raise Exception("LLM request timed out")
                        continue

                    if not content:
                        logger.warning(f"OUTLINE: {indent}Empty LLM response on attempt {attempt + 1}")
                        continue

                    # Enhanced JSON extraction with validation
                    outline = ProposalUtilities.extract_json_from_brackets(content)

                    if outline is None:
                        logger.warning(f"OUTLINE: {indent}Failed to extract JSON on attempt {attempt + 1}")
                        logger.debug(f"OUTLINE: {indent}Raw content: {content[:200]}...")
                        continue

                    # Validate required fields and content quality against table of contents
                    validation_errors = self._validate_outline_against_toc(outline, section, indent)

                    if validation_errors:
                        logger.warning(f"OUTLINE: {indent}Validation errors on attempt {attempt + 1}: {validation_errors}")
                        if attempt < max_attempts - 1:
                            continue
                        else:
                            # Use outline with warnings for final attempt
                            logger.warning(f"OUTLINE: {indent}Using outline with validation warnings")

                    logger.info(f"OUTLINE: {indent}Successfully generated outline for {section_title}")

                    # Recursively process subsections with depth tracking
                    subsections = section.get("subsections", [])
                    if subsections:
                        outline["subsections"] = []
                        logger.info(f"OUTLINE: {indent}Processing {len(subsections)} subsections")
                        for subsection in subsections:
                            sub_outline = await outline_for_section(subsection, depth + 1)
                            if sub_outline:  # Only add non-empty subsections
                                outline["subsections"].append(sub_outline)

                    return outline

                except Exception as e:
                    logger.error(f"OUTLINE: {indent}Error on attempt {attempt + 1}: {e}")
                    if attempt == max_attempts - 1:
                        raise
                    continue

            # Fallback if all attempts failed
            logger.error(f"OUTLINE: {indent}All attempts failed for {section_title}")
            return {}

        # Build the enhanced nested outline structure with comprehensive tracking
        outlines = []
        total_sections = len(table_of_contents)
        successful_sections = 0

        for i, section in enumerate(table_of_contents, 1):
            logger.info(f"OUTLINE: Processing main section {i}/{total_sections}: {section.get('title', 'Unknown')}")

            try:
                outline = await outline_for_section(section, depth=0)
                if outline:
                    outlines.append(outline)
                    successful_sections += 1
                    logger.info(f"OUTLINE: Successfully processed section {i}/{total_sections}")
                else:
                    logger.warning(f"OUTLINE: Empty outline returned for section {i}/{total_sections}")
            except Exception as e:
                logger.error(f"OUTLINE: Failed to process section {i}/{total_sections}: {e}")
                # Continue with other sections rather than failing completely
                continue

        # Generate comprehensive summary
        logger.info(f"OUTLINE: Generation complete - {successful_sections}/{total_sections} sections successful")

        return {
            "outlines": outlines,
            "generation_summary": {
                "total_sections": total_sections,
                "successful_sections": successful_sections,
                "success_rate": (successful_sections / total_sections * 100) if total_sections > 0 else 0,
                "enhanced_features": [
                    "Government compliance validation",
                    "Multi-query context retrieval",
                    "Comprehensive error handling",
                    "Quality assurance checks"
                ]
            }
        }
    
    async def generate_outline_markdown(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        table_of_contents: List[Dict[str, Any]],
        is_rfp: bool = True,
        max_retrieval_results: int = 6
    ) -> Dict[str, Any]:
        """
        Produce a human-ready proposal outline in Markdown for each TOC entry.
        - Uses vector DB (tenant/opportunity) for requirement snippets & references.
        - LLM must return Markdown-only content (no placeholders like {{}} or 'TBD').
        - Each section's output includes: title, page_limit, purpose, required information,
        recommended structure (bullets/headings), required tables/diagrams, exact
        quoted references extracted from the vector DB.
        Returns:
        {
            "outlines": [
            { "title": str, "page_limit": int, "markdown": str, "references": [str], "validation_warnings": [] }
            ],
            "generation_summary": {...}
        }
        """

        logger.info(f"OUTLINE_MD: Start for opportunity={opportunity_id} tenant={tenant_id} sections={len(table_of_contents)}")

        # Tokens not allowed in final markdown (no placeholders)
        FORBIDDEN_TOKENS = ["{{", "}}", "TBD", "TO BE", "PLACEHOLDER", "TBA"]

        # Query templates to fetch comment/requirement snippets and evaluation hints
        QUERY_TEMPLATES = [
            "Exact RFP instruction / comment for section: ",
            "Evaluation criteria & scoring notes for section: ",
            "Formatting & page limits for section: ",
            "Suggested deliverables and required tables for section: ",
            "Past-performance or example language relevant to section: "
        ]

        async def fetch_context_chunks(title: str):
            """Fetch and deduplicate context chunks from vector DB for the given title."""
            chunks = []
            try:
                async for db in get_kontratar_db():
                    collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
                    seen = set()
                    for template in QUERY_TEMPLATES:
                        query = f"{template}{title}"
                        try:
                            results = await asyncio.wait_for(
                                self.chroma_service.get_relevant_chunks(db, collection_name, query, n_results=max_retrieval_results),
                                timeout=20.0
                            )
                            for r in results:
                                text = str(r).replace("\n", " ").replace("\t", " ").strip()
                                if text and text not in seen:
                                    seen.add(text)
                                    chunks.append(text)
                        except asyncio.TimeoutError:
                            logger.warning(f"OUTLINE_MD: timeout fetching for '{title}' query '{query[:80]}'")
                        except Exception as e:
                            logger.error(f"OUTLINE_MD: error fetching '{query[:80]}': {e}")
                    break
            except Exception as e:
                logger.error(f"OUTLINE_MD: DB connection error: {e}")
            return chunks

        # System prompt — force Markdown-only, no placeholders
        system_prompt = (
            "ROLE: Senior Government Proposal Outline Editor.\n"
            "INSTRUCTION: Return **MARKDOWN ONLY**. Do NOT output JSON, XML or any placeholders like {{...}} or 'TBD'.\n"
            "Do NOT invent requirements. Use ONLY the CONTEXT_CHUNKS provided. Include exact requirement snippets in a 'References' block.\n"
            "Output structure (Markdown) MUST include: Purpose, Required Information, Suggested Structure with headings/bullets, Required Tables/Diagrams, References. Use numbered headings (##, ###) and bullets.\n"
            "Tone: professional, prescriptive, actionable. No repetition of formatting rules unless they affect substance."
        )

        # User prompt template to instruct the LLM for each section
        user_prompt_template = (
            "SECTION_PAYLOAD\n"
            "Description (from TOC): {description}\n"
            "Page limit (exact): {page_limit}\n\n"
            "CONTEXT_CHUNKS:\n{context}\n\n"
            "TASK: Using ONLY the CONTEXT_CHUNKS, produce a **MARKDOWN** outline to guide writing this section. "
            "Include the following labeled blocks:\n\n"
            "## {title}  (Page limit: {page_limit} pages)\n\n"
            "**Purpose:** one short sentence.\n\n"
            "**Required Information:** bullet list of every mandatory item to include (derived from context). Each bullet should include the exact requirement snippet if present.\n\n"
            "**Suggested Structure & Headings:**  provide the exact sequence of headings/subheadings and what to place under each (use bullets and should be indented). Do NOT include 'Introduction' as a suggested heading since it is redundant.\n\n"
            "**Required Tables / Diagrams:** list and briefly describe required tables/figures.\n\n"
            "**References:** include exact snippets from CONTEXT_CHUNKS that impose requirements; prefix each with the source marker if present (e.g., 'Commented [SL2]: ...').\n\n"
            "CONSTRAINTS: 1) No placeholders. 2) No invented content. 3) If a mandatory instruction exists in context but cannot be satisfied fully, include the exact quote under References.\n\n"
            "END.\n"
        )

        # Simple validator to ensure final markdown conforms to rules and includes references
        def validate_markdown(markdown_text: str, reference_snippets: List[str]) -> List[str]:
            warnings = []
            up = markdown_text.upper()
            for tok in FORBIDDEN_TOKENS:
                if tok in up:
                    warnings.append(f"Forbidden token found in markdown: {tok}")
            # each reference snippet should appear (or a substring) in the markdown reference block
            missing_refs = []
            for ref in reference_snippets:
                # compare normalized strings (short length) to avoid tiny differences
                norm_ref = " ".join(ref.split()[:30]).strip()
                if norm_ref and norm_ref not in markdown_text:
                    missing_refs.append(ref)
            if missing_refs:
                warnings.append(f"{len(missing_refs)} reference snippet(s) not quoted verbatim in markdown References block.")
            # ensure headings present
            if "## " not in markdown_text:
                warnings.append("Expected at least one '##' heading in markdown output.")
            # ensure Purpose and References sections exist
            if "**Purpose:**" not in markdown_text or "**References:**" not in markdown_text:
                warnings.append("Missing required Markdown blocks (Purpose or References).")
            return warnings

        # Core worker to produce a section's markdown
        async def produce_section_markdown(section: Dict[str, Any], depth: int = 0) -> Dict[str, Any]:
            title = section.get("title", "").strip()
            description = section.get("description", "").strip()
            page_limit = int(section.get("page_limit", 2))

            if not title:
                logger.warning("OUTLINE_MD: skipping empty title")
                return {}

            logger.info(f"OUTLINE_MD: generating for '{title}'")

            # fetch context
            chunks = await fetch_context_chunks(title)
            context_text = "\n\n".join(chunks[:max_retrieval_results]) if chunks else "No contextual snippets found."

            # build LLM messages
            system_msg = system_prompt
            user_msg = user_prompt_template.format(title=title, description=description or "N/A", page_limit=page_limit, context=context_text)

            # call LLM (deterministic)
            try:
                raw = await asyncio.to_thread(self.llm.invoke, [{"role":"system","content":system_msg}, {"role":"user","content":user_msg}], temperature=0.0, max_tokens=2200)
                md = str(raw.content).strip()
            except Exception as e:
                logger.error(f"OUTLINE_MD: LLM error for '{title}': {e}")
                md = ""  # fail-safe: return minimal structure below

            # Ensure only markdown returned: strip leading/trailing non-markdown if any
            # (We trust the system prompt but still clamp)
            if not md.startswith("#") and not md.startswith("##"):
                # Try to recover by prepending header
                md = f"## {title} (Page limit: {page_limit} pages)\n\n" + md

            # Validate references: pick quoted lines from chunks (we expect LLM to include them)
            reference_snippets = []
            # heuristic: extract any "Commented [XXX]:" fragments from chunks
            for c in chunks:
                if "Commented [" in c or "Commented[" in c:
                    reference_snippets.append(c)
                else:
                    # if chunk looks like an instruction, include a truncated snippet as reference
                    if len(c.split()) > 6:
                        reference_snippets.append(" ".join(c.split()[:40]) + ("..." if len(c.split())>40 else ""))

            validation_warnings = validate_markdown(md, reference_snippets)

            # Recursively handle subsections (if present)
            subsections = section.get("subsections", []) or []
            sub_results = []
            if subsections:
                for sub in subsections:
                    sub_out = await produce_section_markdown(sub, depth + 1)
                    if sub_out:
                        sub_results.append(sub_out)
                # append subsections markdown below this section's markdown to keep one combined doc per top-level
                if sub_results:
                    # combine their markdowns under a new "### Subsections" block
                    subsections_md = "\n\n".join([s["markdown"] for s in sub_results])
                    md = md + "\n\n### Subsections\n\n" + subsections_md

            return {
                "title": title,
                "page_limit": page_limit,
                "markdown": md,
                "references": reference_snippets,
                "validation_warnings": validation_warnings
            }

        # Process all top-level sections sequentially (or in parallel if you prefer)
        outlines = []
        for sec in table_of_contents:
            try:
                item = await produce_section_markdown(sec, depth=0)
                if item:
                    outlines.append(item)
            except Exception as e:
                logger.error(f"OUTLINE_MD: failed for section '{sec.get('title')}': {e}")
                continue

        logger.info(f"OUTLINE_MD: done. Produced {len(outlines)} section outlines.")
        return {
            "outlines": outlines,
            "generation_summary": {
                "total_sections": len(table_of_contents),
                "produced": len(outlines),
                "success_rate": (len(outlines) / len(table_of_contents) * 100) if table_of_contents else 0
            }
        }


    def _validate_outline_against_toc(self, outline: Dict[str, Any], toc_section: Dict[str, Any], indent: str = "") -> List[str]:
        """Validate outline strictly against table of contents requirements"""
        errors = []

        expected_title = toc_section.get("title", "")
        expected_page_limit = toc_section.get("page_limit", 2)
        expected_description = toc_section.get("description", "")

        # Required fields validation
        required_fields = ["title", "content", "page_limit", "purpose", "rfp_vector_db_query",
                          "client_vector_db_query", "custom_prompt", "references"]

        for field in required_fields:
            if field not in outline:
                errors.append(f"Missing required field: {field}")
            elif not outline[field] or (isinstance(outline[field], str) and len(outline[field].strip()) < 10):
                errors.append(f"Field '{field}' is empty or too short")

        # STRICT Title validation - must match TOC exactly
        if "title" in outline:
            actual_title = outline["title"]
            if actual_title != expected_title:
                errors.append(f"CRITICAL: Title must be EXACTLY '{expected_title}', got '{actual_title}'")

        # STRICT Page limit validation - must match TOC exactly
        if "page_limit" in outline:
            actual_page_limit = outline["page_limit"]
            if actual_page_limit != expected_page_limit:
                errors.append(f"CRITICAL: Page limit must be EXACTLY {expected_page_limit}, got {actual_page_limit}")

        # Content quality validation
        if "content" in outline:
            content = outline["content"]
            if len(content) < 200:
                errors.append("Content guidance too short (minimum 200 characters)")
            if any(placeholder in content.lower() for placeholder in ["[", "]", "tbd", "todo", "placeholder"]):
                errors.append("Content contains placeholders or brackets")

        # Custom prompt validation - must specify exact page limit
        if "custom_prompt" in outline:
            prompt = outline["custom_prompt"]
            if len(prompt) < 300:
                errors.append("Custom prompt too short (minimum 300 characters)")
            if "step" not in prompt.lower():
                errors.append("Custom prompt missing step-by-step instructions")

            # Check that custom prompt mentions the correct page limit (flexible format)
            page_limit_mentioned = False
            page_limit_patterns = [
                str(expected_page_limit) + " page",
                str(expected_page_limit) + "-page",
                f"{expected_page_limit} pages",
            ]

            # Special cases for numbers written as words
            if expected_page_limit == 1:
                page_limit_patterns.extend(["one page", "one-page", "single page"])
            elif expected_page_limit == 2:
                page_limit_patterns.extend(["two page", "two-page"])
            elif expected_page_limit == 3:
                page_limit_patterns.extend(["three page", "three-page"])
            elif expected_page_limit == 4:
                page_limit_patterns.extend(["four page", "four-page"])

            for pattern in page_limit_patterns:
                if pattern.lower() in prompt.lower():
                    page_limit_mentioned = True
                    break

            if not page_limit_mentioned:
                errors.append(f"Custom prompt must specify exactly {expected_page_limit} pages")

        if errors:
            logger.warning(f"OUTLINE: {indent}TOC validation failed for {expected_title}: {errors}")

        return errors

    def _process_draft_content(self, content: str, is_cover_letter: bool, section_title: str, indent: str = "") -> str:
        """Enhanced content processing that preserves cover letter formatting"""
        try:
            # Remove first markdown title if present
            text = remove_first_markdown_title_regex(content)
            text = text.strip()

            if is_cover_letter:
                # For cover letters, preserve formatting and only remove markdown code blocks
                logger.info(f"DRAFT: {indent}Processing cover letter content (preserving formatting)")

                # Only remove markdown code blocks, preserve other formatting
                if text.startswith('```'):
                    first_newline = text.find('\n')
                    if first_newline != -1:
                        text = text[first_newline + 1:]

                if text.endswith('```'):
                    text = text[:-3]

                # Remove only markdown code block markers, preserve other content
                text = text.replace('```markdown', '').replace('```', '')
                text = text.strip()

                # Preserve line breaks and formatting for cover letters
                logger.info(f"DRAFT: {indent}Cover letter processed, length: {len(text)} chars")

            else:
                # For technical sections, apply standard processing
                logger.info(f"DRAFT: {indent}Processing technical section content")

                if text.startswith('```'):
                    first_newline = text.find('\n')
                    if first_newline != -1:
                        text = text[first_newline + 1:]

                if text.endswith('```'):
                    text = text[:-3]
                text = text.replace('```', '')
                text = text.strip()

                logger.info(f"DRAFT: {indent}Technical section processed, length: {len(text)} chars")

            return text

        except Exception as e:
            logger.error(f"DRAFT: {indent}Error processing content: {e}")
            return content.strip()

    def _validate_draft_quality(self, content: str, section_title: str, is_cover_letter: bool, indent: str = "") -> List[str]:
        """Validate draft content quality against government standards"""
        errors = []

        try:
            # Basic content validation
            if len(content.strip()) < 50:
                errors.append("Content too short (minimum 50 characters)")

            placeholder_patterns = ["[", "]", "tbd", "todo", "placeholder", "xxx", "yyy"]
            if not is_cover_letter:
                # Stricter validation for technical sections
                for pattern in placeholder_patterns:
                    if pattern in content.lower():
                        errors.append(f"Content contains placeholder: {pattern}")
            else:
                critical_placeholders = ["[replace", "[insert", "tbd", "todo", "xxx"]
                for pattern in critical_placeholders:
                    if pattern in content.lower():
                        errors.append(f"Cover letter contains placeholder: {pattern}")

            generic_phrases = ["lorem ipsum", "sample text", "example content", "generic"]
            for phrase in generic_phrases:
                if phrase in content.lower():
                    errors.append(f"Content contains generic phrase: {phrase}")

            if is_cover_letter:
                required_elements = ["dear", "sincerely", "regards"]
                found_elements = sum(1 for element in required_elements if element in content.lower())
                if found_elements < 2:
                    errors.append("Cover letter missing required formal elements")

                if len(content) < 200:
                    errors.append("Cover letter too short (minimum 200 characters)")
            else:
                if len(content) < 100:
                    errors.append("Technical content too short (minimum 100 characters)")

                # More intelligent relevance checking
                title_words = section_title.lower().split()
                content_lower = content.lower()

                # Filter out common words and section identifiers
                meaningful_words = [
                    word for word in title_words
                    if len(word) > 3 and word not in [
                        'tab', 'factor', 'section', 'part', 'volume', 'appendix',
                        'attachment', 'exhibit', 'schedule', 'item', 'element'
                    ]
                ]

                if meaningful_words:  # Only check if there are meaningful words
                    relevant_words = sum(1 for word in meaningful_words if word in content_lower)
                    # More lenient threshold - at least one meaningful word should appear
                    if relevant_words == 0 and len(meaningful_words) > 2:
                        errors.append("Content may not be sufficiently relevant to section title")

            if errors:
                logger.warning(f"DRAFT: {indent}Quality validation failed for {section_title}: {errors}")

        except Exception as e:
            logger.error(f"DRAFT: {indent}Error during validation: {e}")
            errors.append(f"Validation error: {str(e)}")

        return errors

    def _is_key_personnel_section_fast(self, title: str, description: str) -> tuple[bool, bool]:
        """
        Fast personnel detection using keyword matching first, then determining if LLM is needed.
        Returns (is_personnel, needs_llm_verification)
        """
        title_lower = title.lower()
        desc_lower = description.lower()

        # Strong positive indicators - definitely personnel sections
        strong_personnel_keywords = [
            'key personnel', 'personnel', 'staff', 'team', 'qualifications', 'resumes',
            'cv', 'curriculum vitae', 'biography', 'biographies', 'staffing',
            'organizational chart', 'project manager', 'technical lead', 'labor categories',
            'employee', 'contractor', 'consultant', 'specialist', 'expert'
        ]

        # Strong negative indicators - definitely NOT personnel sections
        strong_non_personnel_keywords = [
            'technical approach', 'methodology', 'solution', 'tool', 'software',
            'pricing', 'cost', 'budget', 'schedule', 'timeline', 'risk management',
            'quality assurance', 'testing', 'deployment', 'infrastructure',
            'architecture', 'design', 'implementation', 'maintenance'
        ]

        # Check for strong positive matches
        for keyword in strong_personnel_keywords:
            if keyword in title_lower or keyword in desc_lower:
                logger.info(f"Fast personnel detection (POSITIVE): '{title}' - matched '{keyword}'")
                return True, False

        # Check for strong negative matches
        for keyword in strong_non_personnel_keywords:
            if keyword in title_lower:
                logger.info(f"Fast personnel detection (NEGATIVE): '{title}' - matched '{keyword}'")
                return False, False

        logger.info(f"Fast personnel detection (AMBIGUOUS): '{title}' - needs LLM verification")
        return False, True

    async def _is_key_personnel_section(self, title: str, description: str) -> bool:
        """
        Optimized personnel detection with fast keyword matching + selective LLM verification.
        """
        is_personnel, needs_llm = self._is_key_personnel_section_fast(title, description)

        if not needs_llm:
            return is_personnel

        logger.info(f"Using LLM for ambiguous personnel detection: '{title}'")

        system_prompt = """
You are an expert at analyzing government proposal sections. Your task is to determine if a proposal section is about key personnel, staff, team members, or qualifications.

Return ONLY "true" or "false" - nothing else.

A section is about key personnel if it involves:
- Staff qualifications and experience
- Team member profiles or resumes
- Personnel assignments or roles
- Key person biographies or credentials
- Staffing plans or organizational charts
- Employee qualifications or certifications
- Project team composition
- Management team descriptions
- Technical staff expertise
- Labor categories or skill sets

A section is NOT about key personnel if it's about:
- Technical approaches or methodologies
- Project management processes
- Company capabilities or past performance
- Technical solutions or tools
- Pricing or cost proposals
- Schedules or timelines
- Risk management
- Quality assurance processes
"""

        user_prompt = f"""
Section Title: {title}
Section Description: {description}

Is this section about key personnel, staff, or team qualifications?
"""

        try:
            messages = [
                ("system", system_prompt),
                ("human", user_prompt)
            ]
            try:
                result = await asyncio.wait_for(
                    asyncio.to_thread(self.llm.invoke, messages),
                    timeout=120  # 2 minute timeout
                )
                response = str(result.content).strip().lower()
            except asyncio.TimeoutError:
                logger.error(f"LLM invocation timed out for personnel detection: {title}")
                raise Exception("LLM request timed out")

            # Parse the response
            is_personnel = response == "true" or "true" in response

            logger.info(f"LLM personnel detection for '{title}': {is_personnel} (response: {response})")
            return is_personnel

        except Exception as e:
            logger.error(f"Error in LLM personnel detection: {e}")
            # Fallback to keyword matching if LLM fails
            keywords = ['key personnel', 'personnel', 'staff', 'team', 'qualifications', 'resumes']
            fallback_result = any(keyword in title.lower() for keyword in keywords)
            logger.info(f"Using fallback keyword detection: {fallback_result}")
            return fallback_result


    ## Enhanced draft generation with government standards
    async def generate_draft(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        client_short_name: str,
        tenant_metadata: str,
        table_of_contents: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Generate a draft for each section and subsection in the table of contents.
        The drafts are nested to match the table of contents hierarchy.
        """

        logger.info(f"DRAFT: Starting enhanced draft generation for opportunity {opportunity_id}")
        logger.info(f"DRAFT: Processing {len(table_of_contents)} main sections")

        record = await self.get_opportunity(opportunity_id, tenant_id, source)

        personnel_cache = {}
        personnel_sections = []

        def collect_personnel_sections(sections, depth=0):
            for section in sections:
                section_title = section.get("title", "").strip()
                section_desc = section.get("description", "").strip()

                if section_title:
                    is_personnel, needs_llm = self._is_key_personnel_section_fast(section_title, section_desc)
                    if is_personnel or needs_llm:
                        personnel_sections.append({
                            'section': section,
                            'is_personnel': is_personnel,
                            'needs_llm': needs_llm,
                            'depth': depth
                        })

                if section.get("subsections"):
                    collect_personnel_sections(section["subsections"], depth + 1)

        collect_personnel_sections(table_of_contents)

        if personnel_sections:
            logger.info(f"DRAFT: Pre-fetching personnel data for {len(personnel_sections)} potential personnel sections")
            try:
                personnel_data = await self.key_personnel_service.generate_key_personnel_content(
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    source=source,
                    section_requirements="General key personnel requirements"
                )
                personnel_cache['data'] = personnel_data
                personnel_cache['has_data'] = personnel_data.get("has_real_resumes", False)
                logger.info(f"DRAFT: Personnel data cached - has_real_resumes: {personnel_cache['has_data']}")
            except Exception as e:
                logger.error(f"DRAFT: Error pre-fetching personnel data: {e}")
                personnel_cache['data'] = None
                personnel_cache['has_data'] = False

        @retry(
            stop=stop_after_attempt(5),
            wait=wait_fixed(3),
            retry=retry_if_exception_type(Exception),
            reraise=True
        )
        async def draft_for_section(section: Dict[str, Any], depth: int = 0) -> Dict[str, Any]:
            """Enhanced section draft generation with government compliance and cover letter handling"""
            section_title = section.get("title", "").strip()
            section_desc = section.get("description", "").strip()
            section_number = section.get("number", "").strip()

            indent = "  " * depth
            logger.info(f"DRAFT: {indent}Processing section {section_number} - {section_title}")

            if not section_title:
                logger.warning(f"DRAFT: {indent}Empty section title found, skipping")
                return {}

            is_cover_letter = any(keyword in section_title.lower() for keyword in [
                'cover', 'transmittal', 'letter', 'introduction letter', 'proposal letter'
            ])

            logger.info(f"DRAFT: {indent}Cover letter detected: {is_cover_letter}")

            # Enhanced context retrieval with multiple targeted queries
            context = ""
            client_context_str = ""

            try:
                async for db in get_kontratar_db():
                    opportunity_collection = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id

                    # Multiple targeted queries for better context
                    chroma_queries = [
                        f"Requirements and specifications for {section_title} section",
                        f"Content and deliverables expected in {section_title}",
                        f"Evaluation criteria and standards for {section_title}"
                    ]

                    all_chunks = []
                    for query in chroma_queries:
                        try:
                            chunks = await asyncio.wait_for(
                                self.chroma_service.get_relevant_chunks(db, opportunity_collection, query, n_results=2),
                                timeout=30.0
                            )
                            all_chunks.extend(chunks)
                        except asyncio.TimeoutError:
                            logger.warning(f"DRAFT: {indent}ChromaDB timeout for query: {query[:50]}...")
                        except Exception as e:
                            logger.error(f"DRAFT: {indent}ChromaDB error: {e}")

                    if all_chunks:
                        rfp_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in all_chunks]
                        context = "\n".join(rfp_context)
                        logger.info(f"DRAFT: {indent}Retrieved {len(all_chunks)} RFP context chunks")

                    # Client context retrieval
                    client_collection = f"{tenant_id}_{client_short_name}"
                    client_query = f"Company capabilities and experience relevant to {section_title}"

                    try:
                        client_chunks = await asyncio.wait_for(
                            self.chroma_service.get_relevant_chunks(db, client_collection, client_query, n_results=3),
                            timeout=30.0
                        )
                        client_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in client_chunks]
                        client_context_str = "\n".join(client_context)
                        logger.info(f"DRAFT: {indent}Retrieved {len(client_chunks)} client context chunks")
                    except asyncio.TimeoutError:
                        logger.warning(f"DRAFT: {indent}Client context timeout")
                    except Exception as e:
                        logger.error(f"DRAFT: {indent}Client context error: {e}")

                    break

            except Exception as e:
                logger.error(f"DRAFT: {indent}Database connection error: {e}")
                context = f"Section: {section_title}\nDescription: {section_desc}"
                client_context_str = "Professional services company with government contracting expertise."

            is_key_personnel = await self._is_key_personnel_section(section_title, section_desc)

            personnel_context = ""
            if is_key_personnel:
                logger.info(f"DRAFT: {indent}Key personnel section detected, using cached data")

                if personnel_cache.get('has_data', False):
                    cached_data = personnel_cache['data']
                    personnel_context = f"\n\nREAL PERSONNEL DATA:\n{cached_data['content']}"
                    logger.info(f"DRAFT: {indent}Using cached real resume data for {cached_data['personnel_count']} personnel")
                elif personnel_cache.get('data'):
                    logger.warning(f"DRAFT: {indent}No real resume data available, will generate based on requirements only")
                    personnel_context = f"\n\nPERSONNEL REQUIREMENTS: Generate key personnel section based on the requirements in the section description. Do not use placeholder names or fabricated information."
                else:
                    logger.warning(f"DRAFT: {indent}No personnel data available in cache")

            # # Enhance content with academic research for technical sections
            # research_context = ""
            # if not is_cover_letter and not is_key_personnel:
            #     logger.info(f"DRAFT: {indent}Enhancing section with academic research")
            #     try:
            #         research_enhancement = await self.research_enhancement_service.enhance_section_with_research(
            #             section_title=section_title,
            #             section_content=f"{section_desc}\n{context}",
            #             opportunity_context=getattr(record, 'description', ''),
            #             max_papers=3
            #         )

            #         if research_enhancement.get("research_added", False):
            #             research_context = f"\n\nRESEARCH ENHANCEMENT:\n{research_enhancement['enhancement_summary']}"
            #             logger.info(f"DRAFT: {indent}Added research from {research_enhancement['papers_found']} papers")

            #     except Exception as e:
            #         logger.error(f"DRAFT: {indent}Error enhancing with research: {e}")

            if is_cover_letter:
                system_prompt = '''
                    **ROLE:** Government Proposal Cover Letter Expert
                    **MISSION:** Generate professional, compliant cover letters that meet federal proposal standards.

                    **CRITICAL COVER LETTER REQUIREMENTS:**
                    1. ZERO placeholders, brackets, TBD, TODO, or incomplete information
                    2. INCLUDE proper business letter format with date, addresses, and signatures
                    3. EXTRACT real company information from provided metadata - NO fabrication
                    4. REFERENCE specific opportunity details
                    5. DEMONSTRATE understanding of the government requirement
                    6. MAINTAIN professional, formal tone throughout
                    7. INCLUDE proper contact information and company credentials
                    8. COMPLY with standard business letter formatting

                    **COVER LETTER STRUCTURE:**
                    - Proper letterhead with company information
                    - Current date
                    - Government agency address (if available)
                    - Reference line with opportunity title
                    - Professional salutation
                    - Opening paragraph stating purpose and company interest
                    - Body paragraph highlighting relevant qualifications
                    - Closing paragraph expressing commitment and next steps
                    - Professional closing with signature block
                    - Contact information

                    **CONTENT STANDARDS:**
                    - Use specific company capabilities from metadata
                    - Reference actual opportunity requirements
                    - Demonstrate understanding of government needs
                    - Maintain professional, confident tone
                    - Include relevant certifications or credentials
                    - Show commitment to compliance and quality
                '''
            else:
                system_prompt = '''
                    **ROLE:** Government Proposal Technical Content Expert
                    **MISSION:** Generate professional government proposal content with 100% compliance.

                    **CRITICAL REQUIREMENTS:**
                    1. ZERO placeholders, brackets, TBD, TODO, or incomplete information
                    2. NO contact info in technical sections
                    3. NO repetition of RFP requirements or administrative details
                    4. NO generic marketing language - be specific and evidence-based
                    5. FOCUS EXCLUSIVELY on demonstrating capability for the requested work
                    6. USE concrete methodologies and measurable outcomes
                    7. WRITE for government evaluators assessing technical capability
                    8. COMPLY with page limits and formatting requirements
                    9. NO repetitive phrases like "detailed qualifications repeated from prior response"
                    10. NO meta-commentary about the content being generated
                    11. If personnel data is provided, use ONLY that data - NO fabricated names or details

                    **ABSOLUTELY FORBIDDEN CONTENT:**
                    - [Specific Technology 1], [Specific Technology 2], [Client Name], [Project Name]
                    - [List 3-5 key technical skills], [Insert details here], [Replace with...]
                    - [Project Name Redacted for Confidentiality], [Contact Information]
                    - Empty tables with only headers and no data rows
                    - Generic placeholder names like "[Team Member Name]" or "[Role Title]"
                    - Template language like "To Be Determined" or "Will be provided later"
                    - Incomplete sentences ending with "..." or trailing off

                    **CONTENT STANDARDS:**
                    - Demonstrate HOW you will perform the work with specific processes
                    - Show deep understanding of government requirements
                    - Provide specific methodologies, tools, and success criteria
                    - Focus on capability demonstration, not company marketing
                    - Use professional, direct language
                    - Structure content for easy evaluation (headers, bullets, tables)
                    - Fill ALL tables with realistic, specific data
                    - Use actual technology names not placeholders
                    - Provide concrete examples with measurable outcomes
                    - Include specific timeframes, metrics, and deliverables
                    - Include specific metrics, timelines, and deliverables
                    - Keep paragraphs concise (3-5 sentences maximum)

                    **FORMATTING STANDARDS:**
                    - Use clear section headers and subheaders
                    - Include bullet points for key information
                    - Generate relevant tables in markdown when they support capability demonstration
                    - Tables must be complete with specific data - NO placeholders
                    - Use active voice and strong action verbs
                    - Ensure content is scannable and easy to evaluate
                '''

            if is_cover_letter:
                opportunity_title = getattr(record, 'title', 'Government Consulting Services')
                opportunity_description = getattr(record, 'description', 'Professional services opportunity')

                # Generate current date
                from datetime import datetime
                current_date = datetime.now().strftime("%B %d, %Y")

                # Check if this is a specific type of letter
                title_lower = section_title.lower()
                is_tentative_letter = 'tentative' in title_lower
                is_contingent_letter = 'contingent' in title_lower
                is_offer_letter = 'offer letter' in title_lower and ('tentative' in title_lower or 'contingent' in title_lower)

                if is_tentative_letter or is_contingent_letter or is_offer_letter:
                    user_prompt = f'''
                        Generate a formal tentative/contingent offer letter for employment using the company information provided.

                        **CRITICAL REQUIREMENTS:**
                        - This is NOT a cover letter - it's a conditional job offer letter
                        - NO placeholders, brackets, or [Replace with...] text
                        - NO markdown code blocks or ``` formatting
                        - Extract company information from the tenant metadata provided
                        - Use proper business letter format for employment offers
                        - Include specific conditional terms and requirements
                        - NO fabricated or hallucinated information

                        **LETTER TYPE:** {"Tentative" if is_tentative_letter else "Contingent"} Offer Letter

                        **OPPORTUNITY INFORMATION:**
                        - Date: {current_date}
                        - Position/Project: {opportunity_title}
                        - Description: {opportunity_description}

                        **COMPANY INFORMATION:**
                        {tenant_metadata}

                        **CONTEXT:**
                        {context[:1000] if context else "No specific context available"}

                        **FORMAT REQUIREMENTS:**
                        Generate a complete conditional offer letter with:
                        1. Company letterhead information
                        2. Current date
                        3. Candidate/recipient address placeholder
                        4. Subject line indicating tentative/contingent offer
                        5. Professional salutation
                        6. Opening paragraph extending the conditional offer
                        7. Clear list of conditions that must be met
                        8. Expected start date and compensation details
                        9. Statement about conditional nature of offer
                        10. Instructions for acceptance
                        11. Professional closing with signature block
                        12. Acceptance signature section

                        **REQUIRED CONDITIONS TO INCLUDE:**
                        - Background verification including employment history, education, and criminal record check
                        - Reference checks deemed satisfactory by the team
                        - Verification of eligibility to work
                        - Confirmation of project funding/contract approval (if applicable)

                        Extract the company name, hiring manager, and contact details from the tenant metadata.
                    '''
                else:
                    # Standard cover letter prompt
                    user_prompt = f'''
                        Generate a formal government proposal cover letter using the company information provided.

                    **CRITICAL REQUIREMENTS:**
                    - NO placeholders, brackets, or [Replace with...] text
                    - NO markdown code blocks or ``` formatting
                    - Extract company information from the tenant metadata provided
                    - Use proper business letter format
                    - NO fabricated or hallucinated information
                    - Include proper contact information and company credentials

                    **OPPORTUNITY INFORMATION:**
                    - Date: {current_date}
                    - Opportunity: {opportunity_title}
                    - Description: {opportunity_description}

                    **COMPANY INFORMATION:**
                    {tenant_metadata}

                    **RFP CONTEXT:**
                    {context[:1000] if context else "No specific RFP context available"}

                    **FORMAT REQUIREMENTS:**
                    Generate a complete business letter with:
                    1. Company letterhead information
                    2. Current date
                    3. Government agency address (if available in context)
                    4. Reference line with opportunity title
                    5. Professional salutation
                    6. Opening paragraph stating purpose
                    7. Body paragraph highlighting relevant qualifications
                    8. Closing paragraph expressing commitment
                    9. Professional closing with signature block
                    10. Contact information

                    Extract the company name, contact person, and contact details from the tenant metadata.
                    '''
            else:
                user_prompt = f'''
                    Generate a {section_title} section for a government proposal.

                    **REQUIREMENTS:**
                    - Return ONLY the section content - NO titles or explanations
                    - NO placeholders, brackets, TBD, TODO, or incomplete information
                    - NO contact info, generic marketing, or RFP repetition
                    - Demonstrate HOW you will perform the work with specific processes
                    - Use concrete methodologies and measurable outcomes
                    - Comply with page limits (typically 2-5 pages)
                    - Use clear structure with headers and bullet points where appropriate

                    **SECTION DESCRIPTION:**
                    {section_desc}

                    **RFP CONTEXT:**
                    {context[:1500] if context else "No context provided"}

                    **COMPANY INFORMATION:**
                    {tenant_metadata[:800] if tenant_metadata else ""}
                    {client_context_str[:800] if client_context_str else ""}
                    {personnel_context if personnel_context else ""}

                    Generate professional content demonstrating specific technical capability for this exact requirement.
                '''

            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    logger.info(f"DRAFT: {indent}LLM attempt {attempt + 1}/{max_attempts} for {section_title}")

                    messages = [
                        ("system", system_prompt),
                        ("human", user_prompt)
                    ]

                    try:
                        result = await asyncio.wait_for(
                            asyncio.to_thread(self.llm.invoke, messages),
                            timeout=120.0  # 2 minute timeout
                        )
                        content = str(result.content).strip()
                    except asyncio.TimeoutError:
                        logger.error(f"DRAFT: {indent}LLM timeout on attempt {attempt + 1} for {section_title}")
                        if attempt < max_attempts - 1:
                            continue
                        else:
                            # Generate fallback content
                            content = f"Section content for {section_title}. {section_desc}"

                    if not content:
                        logger.warning(f"DRAFT: {indent}Empty LLM response on attempt {attempt + 1}")
                        continue

                    text = self._process_draft_content(content, is_cover_letter, section_title, indent)

                    if not text or len(text.strip()) < 50:
                        logger.warning(f"DRAFT: {indent}Insufficient content on attempt {attempt + 1}")
                        if attempt < max_attempts - 1:
                            continue

                    validation_errors = self._validate_draft_quality(text, section_title, is_cover_letter, indent)

                    if validation_errors:
                        logger.warning(f"DRAFT: {indent}Validation errors on attempt {attempt + 1}: {validation_errors}")

                        critical_errors = [err for err in validation_errors if
                                         'placeholder' in err.lower() or
                                         'too short' in err.lower() or
                                         'generic phrase' in err.lower()]

                        if critical_errors and attempt < max_attempts - 1:
                            logger.info(f"DRAFT: {indent}Retrying due to critical errors: {critical_errors}")
                            continue
                        elif attempt < max_attempts - 1 and len(validation_errors) > 2:
                            # Only retry if multiple serious issues
                            logger.info(f"DRAFT: {indent}Retrying due to multiple validation issues")
                            continue
                        else:
                            logger.warning(f"DRAFT: {indent}Using draft with validation warnings (final attempt or minor issues)")

                    logger.info(f"DRAFT: {indent}Successfully generated draft for {section_title} ({len(text)} chars)")

                    draft = {
                        "title": f"{section_number} {section_title}".strip(),
                        "content": text,
                        "number": section_number,
                        "is_cover_letter": is_cover_letter,
                        "content_length": len(text),
                        "validation_passed": len(validation_errors) == 0
                    }

                    # Recursively process subsections with depth tracking
                    subsections = section.get("subsections", [])
                    if subsections:
                        draft["subsections"] = []
                        logger.info(f"DRAFT: {indent}Processing {len(subsections)} subsections")
                        for subsection in subsections:
                            sub_draft = await draft_for_section(subsection, depth + 1)
                            if sub_draft:  # Only add non-empty subsections
                                draft["subsections"].append(sub_draft)

                    return draft

                except Exception as e:
                    logger.error(f"DRAFT: {indent}Error on attempt {attempt + 1}: {e}")
                    if attempt == max_attempts - 1:
                        raise
                    continue

            # Fallback if all attempts failed
            logger.error(f"DRAFT: {indent}All attempts failed for {section_title}")
            return {}

        # Build the enhanced nested draft structure with comprehensive tracking
        drafts = []
        total_sections = len(table_of_contents)
        successful_sections = 0
        cover_letters_generated = 0

        for i, section in enumerate(table_of_contents, 1):
            logger.info(f"DRAFT: Processing main section {i}/{total_sections}: {section.get('title', 'Unknown')}")

            try:
                draft = await draft_for_section(section, depth=0)
                if draft:
                    drafts.append(draft)
                    successful_sections += 1
                    if draft.get('is_cover_letter', False):
                        cover_letters_generated += 1
                    logger.info(f"DRAFT: Successfully processed section {i}/{total_sections}")
                else:
                    logger.warning(f"DRAFT: Empty draft returned for section {i}/{total_sections}")
            except Exception as e:
                logger.error(f"DRAFT: Failed to process section {i}/{total_sections}: {e}")
                # Continue with other sections rather than failing completely
                continue

        # Generate comprehensive summary
        logger.info(f"DRAFT: Generation complete - {successful_sections}/{total_sections} sections successful")
        logger.info(f"DRAFT: Cover letters generated: {cover_letters_generated}")

        return {
            "draft": drafts
        }


    async def generate_enhanced_draft(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        client_short_name: str,
        tenant_metadata: str,
        table_of_contents: List[Dict[str, Any]],
        outlines: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Generate enhanced drafts using both table of contents and detailed outlines.
        """

        logger.info(f"ENHANCED_DRAFT: Starting enhanced draft generation for opportunity {opportunity_id}")
        logger.info(f"ENHANCED_DRAFT: Processing {len(table_of_contents)} TOC sections with {len(outlines)} outlines")

        # Validate inputs
        if len(table_of_contents) != len(outlines):
            raise ValueError(f"TOC sections ({len(table_of_contents)}) must match outlines ({len(outlines)})")

        record = await self.get_opportunity(opportunity_id, tenant_id, source)

        personnel_cache = {}
        personnel_sections = []

        def collect_personnel_sections(sections, outlines_list, depth=0):
            for i, section in enumerate(sections):
                section_title = section.get("title", "").strip()
                section_desc = section.get("description", "").strip()

                if section_title and i < len(outlines_list):
                    is_personnel, needs_llm = self._is_key_personnel_section_fast(section_title, section_desc)
                    if is_personnel or needs_llm:
                        personnel_sections.append({
                            'section': section,
                            'outline': outlines_list[i],
                            'is_personnel': is_personnel,
                            'needs_llm': needs_llm,
                            'depth': depth
                        })

                # Process subsections recursively
                subsections = section.get("subsections", [])
                if subsections and i < len(outlines_list):
                    outline_subsections = outlines_list[i].get("subsections", [])
                    collect_personnel_sections(subsections, outline_subsections, depth + 1)

        # Collect personnel sections
        collect_personnel_sections(table_of_contents, outlines)

        drafts = []
        total_sections = len(table_of_contents)
        successful_sections = 0
        cover_letters_generated = 0

        logger.info(f"ENHANCED_DRAFT: Found {len(personnel_sections)} personnel sections")

        for i, (toc_section, outline) in enumerate(zip(table_of_contents, outlines), 1):
            logger.info(f"ENHANCED_DRAFT: Processing section {i}/{total_sections}: {toc_section.get('title', 'Untitled')}")

            # Generate enhanced draft for this section
            section_draft = await self._generate_enhanced_section_draft(
                toc_section, outline, record, personnel_cache, personnel_sections,
                client_short_name, tenant_metadata, opportunity_id, tenant_id, source, i, total_sections
            )

            if section_draft:
                drafts.append(section_draft)
                successful_sections += 1

                # Check if this is a cover letter
                section_title = toc_section.get("title", "").lower()
                if any(keyword in section_title for keyword in ["cover", "transmittal", "letter"]):
                    cover_letters_generated += 1

                logger.info(f"ENHANCED_DRAFT: Successfully processed section {i}/{total_sections}")
            else:
                raise Exception(f"Empty draft returned for section {i}/{total_sections}: {toc_section.get('title', 'Untitled')}")

        # Generate comprehensive summary
        logger.info(f"ENHANCED_DRAFT: Generation complete - {successful_sections}/{total_sections} sections successful")
        logger.info(f"ENHANCED_DRAFT: Cover letters generated: {cover_letters_generated}")

        return {
            "draft": drafts
        }


    async def _generate_enhanced_section_draft(
        self,
        toc_section: Dict[str, Any],
        outline: Dict[str, Any],
        record: Any,
        personnel_cache: Dict[str, Any],
        personnel_sections: List[Dict[str, Any]],
        client_short_name: str,
        tenant_metadata: str,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        section_index: int,
        total_sections: int
    ) -> Dict[str, Any]:
        """
        Generate enhanced draft for a single section using detailed outline information.
        Leverages all outline fields: queries, prompts, references, etc.
        """

        section_title = toc_section.get("title", "Untitled")
        section_desc = toc_section.get("description", "")
        page_limit = toc_section.get("page_limit", 2)

        indent = "  " * 1
        logger.info(f"ENHANCED_DRAFT: {indent}Generating enhanced draft for '{section_title}'")

        try:
            # Extract enhanced information from outline
            outline_content = outline.get("content", "")
            outline_purpose = outline.get("purpose", "")
            rfp_query = outline.get("rfp_vector_db_query", "")
            client_query = outline.get("client_vector_db_query", "")
            custom_prompt = outline.get("custom_prompt", "")
            references = outline.get("references", [])

            logger.info(f"ENHANCED_DRAFT: {indent}Using outline with {len(outline_content)} chars content, custom prompt: {len(custom_prompt)} chars")

            # Enhanced context retrieval using outline queries
            context = ""

            # Use RFP-specific query from outline
            if rfp_query and rfp_query.strip():
                logger.info(f"ENHANCED_DRAFT: {indent}Retrieving RFP context with outline query: {rfp_query[:100]}...")
                try:
                    async for db in get_kontratar_db():
                        collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
                        rfp_chunks = await asyncio.wait_for(
                            self.chroma_service.get_relevant_chunks(db, collection_name, rfp_query, n_results=8),
                            timeout=30.0
                        )
                        if rfp_chunks:
                            context += f"RFP Context:\n{' '.join(rfp_chunks)}\n\n"
                            logger.info(f"ENHANCED_DRAFT: {indent}Retrieved {len(rfp_chunks)} RFP chunks")
                        break
                except Exception as e:
                    raise Exception(f"RFP context retrieval failed: {e}")

            # Use client-specific query from outline
            if client_query and client_query.strip():
                logger.info(f"ENHANCED_DRAFT: {indent}Retrieving client context with outline query: {client_query[:100]}...")
                try:
                    async for db in get_kontratar_db():
                        collection_name = f"{tenant_id}_client_data"
                        client_chunks = await asyncio.wait_for(
                            self.chroma_service.get_relevant_chunks(db, collection_name, client_query, n_results=5),
                            timeout=30.0
                        )
                        if client_chunks:
                            context += f"Client Context:\n{' '.join(client_chunks)}\n\n"
                            logger.info(f"ENHANCED_DRAFT: {indent}Retrieved {len(client_chunks)} client chunks")
                        break
                except Exception as e:
                    raise Exception(f"Client context retrieval failed: {e}")

            # Check if this is a personnel section
            is_personnel_section = any(
                ps['section'].get('title') == section_title for ps in personnel_sections
            )

            # Handle personnel sections with enhanced data
            if is_personnel_section:
                logger.info(f"ENHANCED_DRAFT: {indent}Processing as personnel section")
                personnel_content = await self._generate_enhanced_personnel_content(
                    section_title, outline, personnel_cache, tenant_id, context
                )
                if personnel_content:
                    context += f"Personnel Information:\n{personnel_content}\n\n"

            # Use custom prompt from outline or create enhanced prompt
            if custom_prompt and custom_prompt.strip():
                logger.info(f"ENHANCED_DRAFT: {indent}Using custom prompt from outline")
                final_prompt = custom_prompt
            else:
                logger.info(f"ENHANCED_DRAFT: {indent}Creating enhanced prompt based on outline")
                final_prompt = self._create_enhanced_prompt_from_outline(
                    toc_section, outline, context, client_short_name, is_personnel_section
                )

            # Add references from outline to context
            if references:
                ref_text = "\n".join([f"- {ref}" for ref in references if ref])
                context += f"References:\n{ref_text}\n\n"
                logger.info(f"ENHANCED_DRAFT: {indent}Added {len(references)} references from outline")

            # Generate the draft using enhanced prompt and context
            logger.info(f"ENHANCED_DRAFT: {indent}Generating content with enhanced context ({len(context)} chars)")

            messages = [
                ("system", f"""You are an expert government proposal writer specializing in federal contracting.
Generate professional, compliant content that directly addresses evaluation criteria.

Context Information:
{context}

Client: {client_short_name}
Tenant Metadata: {tenant_metadata}"""),
                ("human", final_prompt)
            ]

            try:
                result = await asyncio.wait_for(
                    asyncio.to_thread(self.llm.invoke, messages),
                    timeout=180  # 3 minute timeout for enhanced generation
                )
                content = str(result.content).strip()
                logger.info(f"ENHANCED_DRAFT: {indent}Generated content: {len(content)} characters")

            except asyncio.TimeoutError:
                raise Exception(f"Enhanced draft generation timed out for '{section_title}'")

            # Process and validate content
            is_cover_letter = any(keyword in section_title.lower() for keyword in ["cover", "transmittal", "letter"])
            processed_content = self._process_draft_content(content, is_cover_letter, section_title, indent)

            # Quality validation
            validation_errors = self._validate_draft_quality(processed_content, section_title, is_cover_letter, indent)
            if validation_errors:
                raise Exception(f"Quality validation failed for '{section_title}': {validation_errors}")

            # Build enhanced draft structure
            draft_section = {
                "title": section_title,
                "content": processed_content,
                "page_limit": page_limit,
                "section_number": toc_section.get("number", str(section_index)),
                "enhanced_features": {
                    "used_outline_queries": bool(rfp_query or client_query),
                    "used_custom_prompt": bool(custom_prompt),
                    "used_references": bool(references),
                    "context_length": len(context),
                    "is_personnel_section": is_personnel_section
                }
            }

            # Add subsections if they exist
            subsections = toc_section.get("subsections", [])
            outline_subsections = outline.get("subsections", [])

            if subsections and outline_subsections:
                logger.info(f"ENHANCED_DRAFT: {indent}Processing {len(subsections)} subsections")
                draft_subsections = []

                for sub_toc, sub_outline in zip(subsections, outline_subsections):
                    sub_draft = await self._generate_enhanced_section_draft(
                        sub_toc, sub_outline, record, personnel_cache, personnel_sections,
                        client_short_name, tenant_metadata, opportunity_id, tenant_id, source,
                        section_index, total_sections
                    )
                    if sub_draft:
                        draft_subsections.append(sub_draft)

                if draft_subsections:
                    draft_section["subsections"] = draft_subsections

            logger.info(f"ENHANCED_DRAFT: {indent}Successfully generated enhanced draft for '{section_title}'")
            return draft_section

        except Exception as e:
            logger.error(f"ENHANCED_DRAFT: {indent}Error generating enhanced draft for '{section_title}': {e}")
            raise Exception(f"Enhanced draft generation failed for '{section_title}': {e}")


    async def _generate_enhanced_personnel_content(
        self,
        section_title: str,
        outline: Dict[str, Any],
        personnel_cache: Dict[str, Any],
        tenant_id: str,
        context: str
    ) -> str:
        """Generate enhanced personnel content using outline information"""
        try:
            # Use outline's client query for personnel data if available
            client_query = outline.get("client_vector_db_query", "")
            if not client_query:
                client_query = f"Key personnel qualifications and experience for {section_title}"

            # Try to get personnel data from cache first
            cache_key = f"{tenant_id}_{section_title}"
            if cache_key in personnel_cache:
                return personnel_cache[cache_key]

            # Retrieve personnel data using enhanced query
            personnel_content = ""
            try:
                async for db in get_kontratar_db():
                    collection_name = f"{tenant_id}_personnel"
                    personnel_chunks = await asyncio.wait_for(
                        self.chroma_service.get_relevant_chunks(db, collection_name, client_query, n_results=5),
                        timeout=30.0
                    )
                    if personnel_chunks:
                        personnel_content = "\n".join(personnel_chunks)
                    break
            except Exception as e:
                raise Exception(f"Personnel content retrieval failed: {e}")

            # Cache the result
            personnel_cache[cache_key] = personnel_content
            return personnel_content

        except Exception as e:
            raise Exception(f"Error generating enhanced personnel content: {e}")


    def _create_enhanced_prompt_from_outline(
        self,
        toc_section: Dict[str, Any],
        outline: Dict[str, Any],
        context: str,
        client_short_name: str,
        is_personnel_section: bool
    ) -> str:
        """Create enhanced prompt using outline information"""

        section_title = toc_section.get("title", "")
        section_desc = toc_section.get("description", "")
        page_limit = toc_section.get("page_limit", 2)

        outline_content = outline.get("content", "")
        outline_purpose = outline.get("purpose", "")

        # Base prompt structure
        prompt = f"""Generate a professional, government-compliant proposal section for "{section_title}".

SECTION REQUIREMENTS:
- Title: {section_title}
- Page Limit: {page_limit} pages
- Description: {section_desc}

OUTLINE GUIDANCE:
{outline_content}

PURPOSE:
{outline_purpose}
"""

        if is_personnel_section:
            prompt += """
PERSONNEL SECTION REQUIREMENTS:
- Include specific qualifications and experience
- Provide detailed role descriptions
- Highlight relevant certifications and training
- Demonstrate team capability and expertise
- Follow government personnel documentation standards
"""
        else:
            prompt += """
TECHNICAL SECTION REQUIREMENTS:
- Address all evaluation criteria directly
- Provide specific technical details and methodologies
- Include measurable outcomes and deliverables
- Demonstrate compliance with all requirements
- Use professional, government-appropriate language
"""

        prompt += f"""
FORMATTING REQUIREMENTS:
- Professional business writing style
- Clear section headers and organization
- Bullet points for key information where appropriate
- Exactly {page_limit} pages of content
- Government proposal standards compliance

CLIENT CONTEXT:
Company: {client_short_name}

Generate comprehensive, detailed content that directly addresses the evaluation criteria and demonstrates clear value to the government customer."""

        return prompt





    async def generate_draft_multi_agent(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        client_short_name: str,
        tenant_metadata: str,
        table_of_contents: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Generate a draft using the multi-agent system.

        This method replaces the traditional generate_draft with a sophisticated
        multi-agent approach that provides:
        - Robust LLM handling with intelligent retries
        - Clean failure handling (no fallback content)
        - Specialized agents for different content types
        - Quality assurance and compliance checking

        Args:
            opportunity_id: Unique identifier for the opportunity
            tenant_id: Tenant identifier
            source: Source of the opportunity (custom, sam, etc.)
            client_short_name: Short name of the client
            tenant_metadata: Metadata about the tenant
            table_of_contents: List of sections to generate

        Returns:
            Dictionary containing generated draft with multi-agent metadata
        """

        logger.info(f"MULTI-AGENT: Starting multi-agent draft generation for opportunity {opportunity_id}")
        logger.info(f"MULTI-AGENT: Processing {len(table_of_contents)} sections with specialized agents")

        try:
            # Import the multi-agent workflow
            from services.proposal.multi_agent.workflow import MultiAgentWorkflow

            # Initialize the workflow
            workflow = MultiAgentWorkflow()

            # Validate workflow readiness
            readiness = await workflow.validate_workflow_readiness()
            if not readiness['ready']:
                logger.error(f"MULTI-AGENT: Workflow not ready: {readiness['issues']}")
                raise Exception(f"Multi-agent workflow not ready: {readiness['issues']}")

            logger.info(f"MULTI-AGENT: Workflow ready with {len(readiness['agent_status'])} agents")

            # Generate content for each section using multi-agent system
            draft_sections = {}
            generation_summary = {
                'total_sections': len(table_of_contents),
                'successful_sections': 0,
                'failed_sections': 0,
                'agent_performance': {},
                'quality_scores': []
            }

            for section in table_of_contents:
                section_title = section.get('title', 'Untitled Section')
                section_content = section.get('description', '') + '\n' + section.get('content', '')
                section_type = self._map_section_to_agent_type(section_title)

                logger.info(f"MULTI-AGENT: Generating section '{section_title}' using {section_type} approach")

                try:
                    # Generate content using multi-agent workflow
                    result = await workflow.generate_section_content(
                        opportunity_id=opportunity_id,
                        tenant_id=tenant_id,
                        section_type=section_type,
                        section_content=section_content,
                        client_short_name=client_short_name
                    )

                    if result['success']:
                        logger.info(f"MULTI-AGENT: Section '{section_title}' generated successfully")
                        logger.info(f"MULTI-AGENT: Quality score: {result.get('quality_score', 'N/A')}")

                        # Store the generated content
                        draft_sections[section_title] = {
                            'title': section_title,
                            'content': result['content'],
                            'quality_score': result.get('quality_score'),
                            'agent_metadata': result.get('workflow_summary', {}),
                            'generation_method': 'multi_agent'
                        }

                        generation_summary['successful_sections'] += 1
                        if result.get('quality_score'):
                            generation_summary['quality_scores'].append(result['quality_score'])

                        # Track agent performance
                        for agent_role, agent_result in result.get('agent_results', {}).items():
                            if agent_role not in generation_summary['agent_performance']:
                                generation_summary['agent_performance'][agent_role] = {
                                    'successes': 0, 'failures': 0, 'total_time': 0
                                }

                            if agent_result.get('success'):
                                generation_summary['agent_performance'][agent_role]['successes'] += 1
                            else:
                                generation_summary['agent_performance'][agent_role]['failures'] += 1

                            generation_summary['agent_performance'][agent_role]['total_time'] += agent_result.get('processing_time', 0)

                    else:
                        logger.error(f"MULTI-AGENT: Section '{section_title}' generation failed: {result.get('error')}")
                        generation_summary['failed_sections'] += 1

                        # NO FALLBACK CONTENT - fail cleanly
                        draft_sections[section_title] = {
                            'title': section_title,
                            'content': None,
                            'error': result.get('error', 'Unknown error'),
                            'generation_method': 'multi_agent_failed'
                        }

                except Exception as e:
                    logger.error(f"MULTI-AGENT: Exception generating section '{section_title}': {e}")
                    generation_summary['failed_sections'] += 1

                    # NO FALLBACK CONTENT - fail cleanly
                    draft_sections[section_title] = {
                        'title': section_title,
                        'content': None,
                        'error': str(e),
                        'generation_method': 'multi_agent_exception'
                    }

            # Calculate overall statistics
            success_rate = (generation_summary['successful_sections'] /
                          generation_summary['total_sections'] * 100
                          if generation_summary['total_sections'] > 0 else 0)

            average_quality = (sum(generation_summary['quality_scores']) /
                             len(generation_summary['quality_scores'])
                             if generation_summary['quality_scores'] else None)

            logger.info(f"MULTI-AGENT: Generation complete - {generation_summary['successful_sections']}/{generation_summary['total_sections']} sections successful ({success_rate:.1f}%)")
            if average_quality:
                logger.info(f"MULTI-AGENT: Average quality score: {average_quality:.1f}/10")

            drafts = []
            for section in table_of_contents:
                section_title = section.get('title', 'Untitled Section')
                section_data = draft_sections.get(section_title, {})

                if section_data.get('content'):
                    # Create draft object in the same format as original generate_draft
                    draft_obj = {
                        'title': section_title,
                        'content': section_data['content'],
                        'number': section.get('number', ''),
                        'description': section.get('description', ''),
                        'is_cover_letter': self._is_cover_letter_section(section_title),
                        'subsections': []  # Multi-agent doesn't handle subsections yet
                    }
                    drafts.append(draft_obj)
                else:
                    # Even failed sections should be included with empty content
                    draft_obj = {
                        'title': section_title,
                        'content': '',
                        'number': section.get('number', ''),
                        'description': section.get('description', ''),
                        'is_cover_letter': self._is_cover_letter_section(section_title),
                        'subsections': []
                    }
                    drafts.append(draft_obj)

            # Return in the exact same format as generate_draft
            return {
                "draft": drafts
            }

        except ImportError as e:
            logger.error(f"MULTI-AGENT: Multi-agent system not available: {e}")
            logger.info(f"MULTI-AGENT: Falling back to traditional generation method")
            # Fallback to traditional method if multi-agent system is not available
            return await self.generate_draft(
                opportunity_id, tenant_id, source, client_short_name,
                tenant_metadata, table_of_contents
            )

        except Exception as e:
            logger.error(f"MULTI-AGENT: Multi-agent generation failed: {e}")
            # NO FALLBACK - fail cleanly to ensure quality
            raise Exception(f"Multi-agent draft generation failed: {e}")

    def _map_section_to_agent_type(self, section_title: str) -> str:
        """
        Map section title to appropriate agent type for the multi-agent system.

        Enhanced to handle different types of letters and prevent LLM from
        repeating cover letter content for other letter types.

        Args:
            section_title: Title of the section

        Returns:
            Agent type string for the multi-agent system
        """
        title_lower = section_title.lower()

        # Enhanced letter detection - handle all types of letters specifically
        if any(term in title_lower for term in [
            'cover letter', 'cover', 'transmittal letter', 'transmittal',
            'introduction letter', 'introduction', 'proposal letter'
        ]):
            return 'cover_letter'

        # Specific handling for other letter types to prevent cover letter repetition
        elif any(term in title_lower for term in [
            'tentative offer letter', 'contingent offer letter', 'offer letter',
            'tentative', 'contingent', 'award letter', 'notification letter'
        ]):
            # Use custom type with specific instructions for these letter types
            return 'custom'

        # Executive content
        elif any(term in title_lower for term in ['executive', 'summary', 'overview']):
            return 'executive_summary'

        # Technical content
        elif any(term in title_lower for term in ['technical', 'approach', 'solution', 'methodology', 'architecture']):
            return 'technical_approach'

        # Management content
        elif any(term in title_lower for term in ['management', 'plan', 'project', 'organization', 'team']):
            return 'management_plan'
        elif any(term in title_lower for term in ['past', 'performance', 'experience', 'case', 'history']):
            return 'past_performance'

        # Compliance and requirements
        elif any(term in title_lower for term in ['compliance', 'requirement', 'regulation', 'standard']):
            return 'compliance'

        # Pricing and financial
        elif any(term in title_lower for term in ['pricing', 'cost', 'budget', 'financial', 'price']):
            return 'pricing'

        # Appendix and supporting materials
        elif any(term in title_lower for term in ['appendix', 'attachment', 'supporting', 'exhibit']):
            return 'appendix'

        # Default to custom for unknown section types
        else:
            return 'custom'

    def _is_cover_letter_section(self, section_title: str) -> bool:
        """
        Determine if a section is a cover letter type section.

        This addresses the issue where LLM kept repeating cover letter content
        for different types of letters (Tentative/Contingent Offer Letter, etc.)

        Args:
            section_title: Title of the section

        Returns:
            True if this is any type of letter section
        """
        title_lower = section_title.lower()

        # Expanded detection for all types of letters
        letter_keywords = [
            'cover', 'letter', 'transmittal', 'introduction',
            'tentative', 'contingent', 'offer', 'proposal letter',
            'cover letter', 'introduction letter', 'transmittal letter'
        ]

        return any(keyword in title_lower for keyword in letter_keywords)
    
