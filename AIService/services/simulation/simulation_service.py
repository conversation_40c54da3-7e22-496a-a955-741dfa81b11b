import json
from loguru import logger
from database import get_customer_db
from database import get_kontratar_db
from utils.embedding_model import KontratarEmbeddings
from sqlalchemy.ext.asyncio import AsyncSession
from controllers.customer.simulations_controller import SimulationsController
from controllers.customer.simulation_queue_controller import SimulationQueueController
from models.customer_models import ClientProfile
from models.kontratar_models import OppsTable
from utils.llm import KontratarLLM
from models.customer_models import OppClientInfo
import datetime
from models.customer_models import Simulations

llm = KontratarLLM(api_url="http://ai.kontratar.com:8080")

embedding_model = KontratarEmbeddings("http://ai.kontratar.com:5000")


class SimulationService:
    async def process_simulation_queue(self, limit: int = 10):
        logger.info("Starting simulation queue processing...")
        # Get up to `limit` new simulations from the queue
        async for db in get_customer_db():
            queue_items = await SimulationQueueController.get_new_items(db, limit=limit)
            break
        if not queue_items:
            logger.info("No new simulation queue items found.")
            return

        for queue_item in queue_items:
            try:
                # Set status to PROCESSING before starting
                async for db in get_customer_db():
                    await SimulationQueueController.update_status(db, queue_item.id, "PROCESSING")
                    break
        
                await self.process_simulation(queue_item)
                # Mark as completed
                async for db in get_customer_db():
                    await SimulationQueueController.update_status(db, queue_item.id, "COMPLETED")
                     # --- Notification logic starts here ---
                    # Get user_id from job_submitted_by and tenant_id from job_instruction
                    user_id = queue_item.job_submitted_by
                    try:
                        job_instruction = json.loads(queue_item.job_instruction)
                        tenant_id = job_instruction.get("tenantId", "SYSTEM")
                    except Exception:
                        tenant_id = "SYSTEM"
                        
                    # Fetch the simulation title using job_id
                    sim_query = await db.execute(
                        Simulations.__table__.select().where(Simulations.job_id == queue_item.job_id)
                    )
                    sim_row = sim_query.first()
                    sim_title = sim_row.title if sim_row and hasattr(sim_row, "title") else queue_item.job_id
                    # Create notification
                    from controllers.customer.proposal_outline_queue_controller import NotificationQueue
                    notif = NotificationQueue(
                        user_id=user_id,
                        tenant_id=tenant_id,
                        job_info=json.dumps({"link": f"simulation-generation/{queue_item.id}"}),
                        status="NEW",
                        title="Simulation Result Available",
                        message=f"Simulation completed successfully for: {sim_title}",
                        created_at=datetime.utcnow()
                    )
                    db.add(notif)
                    await db.commit()
                    # --- Notification logic ends here ---
                    break
            except Exception as e:
                logger.error(f"Error processing simulation queue item {queue_item.id}: {e}")
                async for db in get_customer_db():
                    await SimulationQueueController.update_status(db, queue_item.id, "FAILED")
                    break

    async def process_simulation(self, queue_item):
        logger.info(f"Processing simulation queue item {queue_item.id}...")
        # Parse job instruction
        try:
            job_instruction = json.loads(queue_item.job_instruction)
            logger.debug(f"Parsed job instruction for queue item {queue_item.id}: {job_instruction}")
        except Exception as e:
            logger.error(f"Invalid job_instruction JSON for queue item {queue_item.id}: {e}")
            return

        clients = job_instruction.get("clients", [])
        tenant_id = job_instruction.get("tenantId")
        opp_id = job_instruction.get("oppId")
        source = job_instruction.get("source", "sam")

        # Get technical requirements from OppsTable
        requirements = []
        async for db in get_kontratar_db():
            opp = await db.execute(
                OppsTable.__table__.select().where(OppsTable.notice_id == opp_id)
            )
            opp_row = opp.first()
            break

        if opp_row and opp_row.requirement_text:
            try:
                requirements = json.loads(opp_row.requirement_text)
            except Exception as e:
                logger.warning(f"Could not parse requirements for opp_id {opp_id}: {e}")
                requirements = []
        else:
            logger.warning(f"No requirements found for opp_id {opp_id}.")
            requirements = []

        if not requirements:
            logger.warning(f"No technical requirements to process for simulation {queue_item.id}.")
            simulation_result = json.dumps([])
            simulation_summary = "No technical requirements found for this opportunity."
            await self._store_simulation_result(queue_item, tenant_id, simulation_result, simulation_summary)
            return

        # Get client profiles and their embeddings
        client_embeddings = {}
        async for db in get_customer_db():
            for client in clients:
                # Split client string into short name and profile_id
                if '.' in client:
                    client_short_name, profile_id = client.split('.', 1)
                else:
                    client_short_name = client
                    profile_id = None

                query = OppClientInfo.__table__.select().where(
                    (OppClientInfo.client_short_name == client_short_name) &
                    (OppClientInfo.tenant_id == tenant_id) &
                    (OppClientInfo.profile_id == profile_id)
                )
                profile_row = await db.execute(query)
                profile = profile_row.first()
                if profile and profile.embeddings is not None and len(profile.embeddings) > 0:
                    client_embeddings[client] = profile.embeddings
                else:
                    logger.warning(f"No embedding found for client {client_short_name} (profile_id: {profile_id}, tenant {tenant_id}) in oppclientinfo")
            break
       
        # Process each requirement
        simulation_results = []
        for req in requirements:
            req_text = req.get("requirement", "")
            explanation = req.get("explanation", "")
            weight = req.get("weight", "")
            if not req_text:
                continue
            embedding_input = f"{req_text} {explanation}" if explanation else req_text
            # Generate embedding for requirement
            req_embedding = embedding_model.embed_query(embedding_input)
            client_scores = []
            best_match_client = None
            best_match_score = -1.0

            for idx, client in enumerate(clients):
                if '.' in client:
                    client_short_name, profile_id = client.split('.', 1)
                else:
                    client_short_name = client
                    profile_id = None

                # Format client display name
                if idx == 0 and profile_id:
                    # First client: get profile name from oppclientversions
                    async for db in get_customer_db():
                        profile_row = await db.execute(
                            ClientProfile.__table__.select().where(
                                (ClientProfile.client_short_name == client_short_name) &
                                (ClientProfile.profile_id == profile_id)
                            )
                        )
                        profile = profile_row.first()
                        profile_name = profile.title if profile and profile.title else profile_id
                        display_client = f"{client_short_name}({profile_name})"
                        break
                else:
                    display_client = client_short_name

                embedding = client_embeddings.get(client)
                if embedding is None:
                    continue

                score = self._cosine_similarity(req_embedding, embedding)
                client_scores.append({"client": display_client, "score": round(score, 4)})
                if score > best_match_score:
                    best_match_score = score
                    best_match_client = display_client

            simulation_results.append({
                "requirement": req_text,
                "explanation": explanation,
                "weight": weight,
                "bestMatchClient": best_match_client,
                "bestMatchScore": round(best_match_score, 4),
                "clientRatings": client_scores
            })

        # Generate summary
        logger.info(f"Generating summary for simulation queue item {queue_item.id}")
        simulation_summary = await self._generate_summary(simulation_results)

        # Store results
        logger.info(f"Storing simulation result for queue item {queue_item.id}")
        await self._store_simulation_result(queue_item, tenant_id, json.dumps(simulation_results), simulation_summary)

    def _cosine_similarity(self, vec1, vec2):
        # Simple cosine similarity for two lists
        import numpy as np
        v1 = np.array(vec1)
        v2 = np.array(vec2)
        if v1.shape != v2.shape or v1.shape[0] == 0:
            return 0.0
        dot = np.dot(v1, v2)
        norm1 = np.linalg.norm(v1)
        norm2 = np.linalg.norm(v2)
        if norm1 == 0 or norm2 == 0:
            return 0.0
        return float(dot) / (norm1 * norm2)

    async def _generate_summary(self, simulation_results):
        """
        Uses LLM to generate a summary highlighting what happened in the simulation.
        """
        logger.info("Calling LLM to generate simulation summary...")
        if not simulation_results:
            logger.warning("No simulation results to summarize.")
            return "No best match clients found."

        # Prompt engineering
        system_prompt = (
            "You are a senior government contracting analyst. "
            "Given the simulation results comparing client technical capabilities to federal opportunity requirements, "
            "write a concise summary that starts directly with the overall assessment. "
            "Do NOT include any headers, addresses, dates, or memo formatting. "
            "write a concise summary highlighting which clients best matched the requirements, "
            "where gaps exist, and any notable strengths or weaknesses. "
            "Be clear, professional, and suitable for executive review."
             "Limit the summary to no more than one paragraph."
        )
        user_prompt = (
            "Simulation Results:\n"
            f"{json.dumps(simulation_results, indent=2)}\n\n"
            "Please summarize the results, starting with the overall assessment. "
            "Do not include any memo or letter formatting, headers, addresses, or dates. "
            "Highlight which clients were the best matches for the requirements, "
            "any areas where no client matched well, and any patterns or insights you observe."
            "Limit the summary to no more than one paragraph."
        )

        # Call LLM (assume async call)
        try:
            summary = llm._call(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                max_tokens=2048
            )
            logger.info("LLM summary generated successfully.")
        except Exception as e:
            logger.error(f"LLM summary generation failed: {e}")
            summary = "Summary generation failed due to LLM error."
        if hasattr(summary, "content"):
            summary = summary.content
        return summary


    async def _store_simulation_result(self, queue_item, tenant_id, simulation_result, simulation_summary):
        async for db in get_customer_db():
            existing_sim = await SimulationsController.get_by_job_id(db, queue_item.job_id)
            if existing_sim:
                await SimulationsController.update(
                    db=db,
                    simulation_id=existing_sim.id,
                    title=existing_sim.title,
                    simulation_result=simulation_result,
                    simulation_summary=simulation_summary
                )
                logger.info(f"Updated existing simulation for job_id {queue_item.job_id}")
            else:
                await SimulationsController.add(
                    db=db,
                    tenant_id=tenant_id,
                    job_id=queue_item.job_id,
                    title=f"Simulation for {queue_item.job_id}",
                    simulation_result=simulation_result,
                    simulation_summary=simulation_summary
                )
                logger.info(f"Created new simulation for job_id {queue_item.job_id}")
            break