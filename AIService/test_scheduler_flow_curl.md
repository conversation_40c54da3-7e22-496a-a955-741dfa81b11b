# Complete Scheduler Flow Test with curl

This guide shows how to test the complete scheduler flow using simple curl commands and monitor everything step by step.

## Test Data
- **Opportunity ID**: `vSe1unlCj9`
- **Tenant ID**: `8d9e9729-f7bd-44a0-9cf1-777f532a2db2`
- **Source**: `custom`

## Step 1: Start the Server
```bash
# Start the FastAPI server
cd AIService
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## Step 2: Check Initial Scheduler Status
```bash
# Check scheduler status
curl -X GET "http://localhost:8000/scheduler/status" | jq

# Expected: All schedulers should be stopped initially
```

## Step 3: Start and Enable All Schedulers
```bash
# Start schedulers with 10-second interval
curl -X POST "http://localhost:8000/scheduler/start" \
  -H "Content-Type: application/json" \
  -d '{"interval_seconds": 10}'

# Enable all schedulers
curl -X POST "http://localhost:8000/scheduler/enable"

# Verify they're running
curl -X GET "http://localhost:8000/scheduler/status" | jq
```

## Step 4: Add Custom Opportunity to Queue (Start the Flow)
```bash
# Add custom opportunity to queue - this starts the whole flow
curl -X POST "http://localhost:8000/queue/custom-opps" \
  -H "Content-Type: application/json" \
  -d '{
    "opps_source": "custom",
    "opps_id": "vSe1unlCj9",
    "tenant_id": "8d9e9729-f7bd-44a0-9cf1-777f532a2db2",
    "originating_ip_address": "127.0.0.1"
  }'
```

## Step 5: Monitor Custom Opps Queue Processing
```bash
# Check custom opps queue status (repeat every 10-15 seconds)
curl -X GET "http://localhost:8000/queue/custom-opps/new?limit=10" | jq

# Look for our opportunity_id "vSe1unlCj9" and watch status change:
# NEW -> PROCESSING -> AI_COMPLETED
```

## Step 6: Monitor Database Results
Create a simple monitoring script to check database:

```bash
# Create a simple Python script to check database
cat > check_db_results.py << 'EOF'
import asyncio
import json
from database import get_customer_db
from controllers.customer.custom_opps_controller import CustomOpportunitiesController

async def check_results():
    opportunity_id = "vSe1unlCj9"
    
    async for db in get_customer_db():
        record = await CustomOpportunitiesController.get_by_opportunity_id(db, opportunity_id)
        break
    
    if not record:
        print("❌ No record found")
        return
    
    print(f"✅ Found record: {record.title}")
    
    # Check compliance
    print(f"Content compliance: {'✅' if record.content_compliance else '❌'}")
    print(f"Structure compliance: {'✅' if record.structure_compliance else '❌'}")
    
    # Check TOCs
    toc_count = 0
    for i in range(1, 6):
        field = "toc_text" if i == 1 else f"toc_text_{i}"
        if hasattr(record, field) and getattr(record, field):
            toc_count += 1
    print(f"TOCs generated: {toc_count}/5")
    
    # Check outlines
    outline_count = 0
    for i in range(1, 6):
        field = f"proposal_outline_{i}"
        if hasattr(record, field) and getattr(record, field):
            outline_count += 1
    print(f"Outlines generated: {outline_count}/5")

if __name__ == "__main__":
    asyncio.run(check_results())
EOF

# Run the check
python check_db_results.py
```

## Step 7: Add Proposal to Queue (Final Step)
```bash
# After custom opps processing is complete, add proposal to queue
curl -X POST "http://localhost:8000/queue/proposal" \
  -H "Content-Type: application/json" \
  -d '{
    "job_instruction": "{\"opportunityId\":\"vSe1unlCj9\",\"tenantId\":\"8d9e9729-f7bd-44a0-9cf1-777f532a2db2\",\"clientShortName\":\"adeptengineeringsolutions\",\"opportunityType\":\"custom\",\"profileId\":\"default\",\"setForReview\":true,\"coverPage\":1,\"exportType\":1,\"aiPersonalityId\":1,\"generatedVolumes\":[1,2],\"is_RFP\":true}",
    "opps_id": "vSe1unlCj9",
    "tenant_id": "8d9e9729-f7bd-44a0-9cf1-777f532a2db2",
    "request_type": 1,
    "job_submitted_by": "test_user"
  }'
```

## Step 8: Monitor Proposal Queue Processing
```bash
# Check proposal queue status
curl -X GET "http://localhost:8000/queue/proposal/new?limit=10" | jq

# Look for our opportunity_id and watch status changes
```

## Step 9: Check Final Results
```bash
# Check if proposals were moved to review queue
cat > check_final_results.py << 'EOF'
import asyncio
from database import get_customer_db
from controllers.customer.proposals_in_review_controller import ProposalsInReviewController
from controllers.customer.proposals_format_queue_controller import ProposalsFormatQueueController

async def check_final():
    opportunity_id = "vSe1unlCj9"
    
    async for db in get_customer_db():
        # Check proposals in review
        review_items = await ProposalsInReviewController.get_by_opportunity_id(db, opportunity_id)
        if review_items:
            print(f"✅ Found {len(review_items)} proposals in review")
            for item in review_items:
                print(f"  - Volume {item.volume_number}, Section {item.section_number}")
        else:
            print("❌ No proposals in review")
        
        # Check proposals in format queue
        format_items = await ProposalsFormatQueueController.get_by_opportunity_id(db, opportunity_id)
        if format_items:
            print(f"✅ Found {len(format_items)} proposals in format queue")
        else:
            print("❌ No proposals in format queue")
        
        break

if __name__ == "__main__":
    asyncio.run(check_final())
EOF

python check_final_results.py
```

## Quick Monitoring Loop
```bash
# Simple monitoring loop
while true; do
  echo "=== $(date) ==="
  echo "Custom Opps Queue:"
  curl -s "http://localhost:8000/queue/custom-opps/new?limit=5" | jq '.items[] | select(.opps_id=="vSe1unlCj9") | {status, created_date}'
  
  echo "Proposal Queue:"
  curl -s "http://localhost:8000/queue/proposal/new?limit=5" | jq '.items[] | select(.opps_id=="vSe1unlCj9") | {status, creation_date}'
  
  echo "Database Check:"
  python check_db_results.py
  
  echo "Sleeping 15 seconds..."
  sleep 15
done
```

## Expected Flow Timeline
1. **0-30s**: Custom opps added to queue, status = NEW
2. **30-60s**: Custom opps processing starts, status = PROCESSING
3. **60-300s**: Compliance data and TOCs generated, status = AI_COMPLETED
4. **300-400s**: Outlines generated by proposal_outline_scheduler
5. **400s+**: Add proposal to queue manually
6. **400-600s**: Final proposal drafts generated and moved to review/format

## Troubleshooting Commands
```bash
# Stop all schedulers
curl -X POST "http://localhost:8000/scheduler/stop"

# Check individual scheduler status
curl -X GET "http://localhost:8000/scheduler/custom-opps/status"
curl -X GET "http://localhost:8000/scheduler/proposal/status"

# Enable specific schedulers
curl -X POST "http://localhost:8000/scheduler/custom-opps/enable"
curl -X POST "http://localhost:8000/scheduler/proposal-outline/enable"
curl -X POST "http://localhost:8000/scheduler/proposal/enable"

# Update queue item status manually if needed
curl -X PUT "http://localhost:8000/queue/custom-opps/vSe1unlCj9/status" \
  -H "Content-Type: application/json" \
  -d '{"status": "NEW"}'
```

This approach is much simpler and gives you direct control over each step!
