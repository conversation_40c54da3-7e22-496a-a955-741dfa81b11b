from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from uuid import UUID

from database import get_customer_db
from services.chat.thread_service import ThreadService
from services.chat.enhanced_chat_service import EnhancedChatService
from schemas.chat_schemas import (
    ThreadCreateRequest, ThreadUpdateRequest, ThreadHistoryResponse,
    ThreadListResponse, DeleteThreadResponse, UpdateThreadTitleResponse,
    ChatRequest, ChatResponse, ThreadListRequest
)
from loguru import logger

# Create router
router = APIRouter(prefix="/threads", tags=["threads"])

# Initialize services
enhanced_chat_service = EnhancedChatService()


# Dependency to get user info (you'll need to implement this based on your auth system)
async def get_current_user_info():
    """
    Get current user information from authentication
    This is a placeholder - implement based on your auth system
    """
    # TODO: Implement proper authentication
    return {
        "user_id": 1,  # Replace with actual user ID from JWT/session
        "tenant_id": "default_tenant"  # Replace with actual tenant ID
    }


@router.post("/", response_model=ThreadHistoryResponse)
async def create_thread(
    request: ThreadCreateRequest,
    db: AsyncSession = Depends(get_customer_db),
    user_info: dict = Depends(get_current_user_info)
):
    """Create a new chat thread"""
    try:
        thread = await ThreadService.create_thread(
            db=db,
            tenant_id=user_info["tenant_id"],
            user_id=user_info["user_id"],
            request=request
        )
        
        # Convert to response model
        return ThreadHistoryResponse(
            id=thread.id,
            title=thread.title,
            opportunity_id=thread.opportunity_id,
            source=thread.source,
            created_date=thread.created_date,
            last_activity_date=thread.last_activity_date,
            is_archived=thread.is_archived,
            message_count=thread.message_count,
            summary=thread.summary,
            messages=[],
            metadata=thread.metadata
        )
        
    except Exception as e:
        logger.error(f"Error creating thread: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=List[ThreadListResponse])
async def list_threads(
    limit: int = Query(default=50, ge=1, le=100),
    offset: int = Query(default=0, ge=0),
    include_archived: bool = Query(default=False),
    opportunity_id: Optional[str] = Query(default=None),
    source: Optional[str] = Query(default=None),
    db: AsyncSession = Depends(get_customer_db),
    user_info: dict = Depends(get_current_user_info)
):
    """List chat threads for the current user"""
    try:
        request = ThreadListRequest(
            limit=limit,
            offset=offset,
            include_archived=include_archived,
            opportunity_id=opportunity_id,
            source=source
        )
        
        threads = await ThreadService.list_threads(
            db=db,
            tenant_id=user_info["tenant_id"],
            user_id=user_info["user_id"],
            request=request
        )
        
        return [
            ThreadListResponse(
                id=thread.id,
                title=thread.title,
                opportunity_id=thread.opportunity_id,
                source=thread.source,
                created_date=thread.created_date,
                last_activity_date=thread.last_activity_date,
                is_archived=thread.is_archived,
                message_count=thread.message_count,
                summary=thread.summary
            )
            for thread in threads
        ]
        
    except Exception as e:
        logger.error(f"Error listing threads: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{thread_id}", response_model=ThreadHistoryResponse)
async def get_thread(
    thread_id: UUID,
    db: AsyncSession = Depends(get_customer_db),
    user_info: dict = Depends(get_current_user_info)
):
    """Get a specific thread with its message history"""
    try:
        thread = await ThreadService.get_thread(
            db=db,
            thread_id=thread_id,
            tenant_id=user_info["tenant_id"],
            user_id=user_info["user_id"],
            include_messages=True
        )
        
        if not thread:
            raise HTTPException(status_code=404, detail="Thread not found")
        
        from schemas.chat_schemas import ChatMessageResponse
        messages = [
            ChatMessageResponse(
                id=msg.id,
                role=msg.role,
                content=msg.content,
                created_date=msg.created_date,
                token_count=msg.token_count,
                metadata=msg.metadata
            )
            for msg in thread.messages
        ]
        
        return ThreadHistoryResponse(
            id=thread.id,
            title=thread.title,
            opportunity_id=thread.opportunity_id,
            source=thread.source,
            created_date=thread.created_date,
            last_activity_date=thread.last_activity_date,
            is_archived=thread.is_archived,
            message_count=thread.message_count,
            summary=thread.summary,
            messages=messages,
            metadata=thread.metadata
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting thread {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{thread_id}", response_model=UpdateThreadTitleResponse)
async def update_thread(
    thread_id: UUID,
    request: ThreadUpdateRequest,
    db: AsyncSession = Depends(get_customer_db),
    user_info: dict = Depends(get_current_user_info)
):
    """Update thread properties"""
    try:
        thread = await ThreadService.update_thread(
            db=db,
            thread_id=thread_id,
            tenant_id=user_info["tenant_id"],
            user_id=user_info["user_id"],
            request=request
        )
        
        if not thread:
            raise HTTPException(status_code=404, detail="Thread not found")
        
        return UpdateThreadTitleResponse(
            success=True,
            message="Thread updated successfully",
            thread_id=thread.id,
            new_title=thread.title
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating thread {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{thread_id}", response_model=DeleteThreadResponse)
async def delete_thread(
    thread_id: UUID,
    db: AsyncSession = Depends(get_customer_db),
    user_info: dict = Depends(get_current_user_info)
):
    """Delete a thread and all its messages"""
    try:
        success = await ThreadService.delete_thread(
            db=db,
            thread_id=thread_id,
            tenant_id=user_info["tenant_id"],
            user_id=user_info["user_id"]
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Thread not found")
        
        return DeleteThreadResponse(
            success=True,
            message="Thread deleted successfully",
            thread_id=thread_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting thread {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{thread_id}/messages", response_model=ChatResponse)
async def send_message(
    thread_id: UUID,
    request: ChatRequest,
    db: AsyncSession = Depends(get_customer_db),
    user_info: dict = Depends(get_current_user_info)
):
    """Send a message to a thread"""
    try:
        if not enhanced_chat_service.validate_message(request.message):
            raise HTTPException(status_code=400, detail="Invalid message format")
        
        # Set thread_id from URL
        request.thread_id = thread_id
        
        response = await enhanced_chat_service.chat(
            db=db,
            request=request,
            tenant_id=user_info["tenant_id"],
            user_id=user_info["user_id"],
            streaming=False
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending message to thread {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{thread_id}/messages/stream")
async def send_message_stream(
    thread_id: UUID,
    request: ChatRequest,
    db: AsyncSession = Depends(get_customer_db),
    user_info: dict = Depends(get_current_user_info)
):
    """Send a message to a thread with streaming response"""
    try:
        if not enhanced_chat_service.validate_message(request.message):
            raise HTTPException(status_code=400, detail="Invalid message format")
        
        # Set thread_id from URL
        request.thread_id = thread_id
        
        response_generator = await enhanced_chat_service.chat(
            db=db,
            request=request,
            tenant_id=user_info["tenant_id"],
            user_id=user_info["user_id"],
            streaming=True
        )
        
        return StreamingResponse(
            response_generator,
            media_type="text/plain",
            headers={"Cache-Control": "no-cache"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error streaming message to thread {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
