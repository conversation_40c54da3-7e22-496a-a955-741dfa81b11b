from fastapi import <PERSON><PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.responses import StreamingResponse
from typing import Op<PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUID

from database import get_customer_db
from services.chat.enhanced_chat_service import EnhancedChatService
from schemas.chat_schemas import ChatRequest, ThreadCreateRequest
from services.chat.thread_service import ThreadService
from loguru import logger

# Create router with /chats prefix
router = APIRouter(prefix="/chats", tags=["chat"])

# Initialize enhanced chat service
enhanced_chat_service = EnhancedChatService()

@router.post("/ask")
async def ask_question_stream(
    request: ChatRequest,
    db: AsyncSession = Depends(get_customer_db)
):
    """
    Ask a question and get a streaming response.
    Creates a new thread or uses existing thread.

    Features:
    - Streaming responses for real-time interaction
    - Document context from ChromaDB
    - Web search capability with Gemini (set web_search=true)
    - Automatic conversation history management
    """
    try:
        logger.info(f"Received streaming chat request: {request.opportunity_id}")

        # Validate message
        if not enhanced_chat_service.validate_message(request.message):
            raise HTTPException(status_code=400, detail="Invalid message format")

        # Get streaming response
        response_generator = await enhanced_chat_service.chat(
            db=db,
            request=request,
            tenant_id=request.tenant_id,
            user_id=1,  # Placeholder - not used in new system
            streaming=True
        )

        return StreamingResponse(
            response_generator,
            media_type="text/plain",
            headers={"Cache-Control": "no-cache"}
        )

    except Exception as e:
        logger.error(f"Error in streaming chat: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/threads")
async def list_threads(
    tenant_id: str,
    opportunity_id: Optional[str] = None,
    source: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    include_archived: bool = False,
    db: AsyncSession = Depends(get_customer_db)
):
    """List chat threads for a tenant"""
    try:
        from schemas.chat_schemas import ThreadListRequest

        request = ThreadListRequest(
            limit=limit,
            offset=offset,
            include_archived=include_archived,
            opportunity_id=opportunity_id,
            source=source,
            tenant_id=tenant_id
        )

        threads = await ThreadService.list_threads(db=db, request=request)

        return [
            {
                "id": str(thread.id),
                "title": thread.title,
                "opportunity_id": thread.opportunity_id,
                "tenant_id": thread.tenant_id,
                "source": thread.source,
                "created_date": thread.created_date,
                "last_activity_date": thread.last_activity_date,
                "is_archived": thread.is_archived,
                "message_count": thread.message_count,
                "summary": thread.summary
            }
            for thread in threads
        ]

    except Exception as e:
        logger.error(f"Error listing threads: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/threads/{thread_id}")
async def get_thread(
    thread_id: UUID,
    tenant_id: str,
    db: AsyncSession = Depends(get_customer_db)
):
    """Get a specific thread with messages"""
    try:
        thread = await ThreadService.get_thread(
            db=db,
            thread_id=thread_id,
            tenant_id=tenant_id
        )

        if not thread:
            raise HTTPException(status_code=404, detail="Thread not found")

        # Get messages for the thread
        messages = await ThreadService.get_thread_messages(
            db=db,
            thread_id=thread_id,
            tenant_id=tenant_id
        )

        return {
            "id": str(thread.id),
            "title": thread.title,
            "opportunity_id": thread.opportunity_id,
            "tenant_id": thread.tenant_id,
            "source": thread.source,
            "created_date": thread.created_date,
            "last_activity_date": thread.last_activity_date,
            "is_archived": thread.is_archived,
            "message_count": thread.message_count,
            "summary": thread.summary,
            "messages": [
                {
                    "id": str(msg.id),
                    "role": msg.role,
                    "content": msg.content,
                    "created_date": msg.created_date,
                    "metadata": msg.metadata
                }
                for msg in messages
            ]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting thread {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/threads/{thread_id}")
async def delete_thread(
    thread_id: UUID,
    tenant_id: str,
    db: AsyncSession = Depends(get_customer_db)
):
    """Delete a thread"""
    try:
        success = await ThreadService.delete_thread(
            db=db,
            thread_id=thread_id,
            tenant_id=tenant_id
        )

        if not success:
            raise HTTPException(status_code=404, detail="Thread not found")

        return {"success": True, "message": "Thread deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting thread {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/threads/{thread_id}")
async def update_thread(
    thread_id: UUID,
    tenant_id: str,
    title: Optional[str] = None,
    is_archived: Optional[bool] = None,
    db: AsyncSession = Depends(get_customer_db)
):
    """Update thread properties"""
    try:
        from schemas.chat_schemas import ThreadUpdateRequest

        request = ThreadUpdateRequest(
            title=title,
            is_archived=is_archived
        )

        thread = await ThreadService.update_thread(
            db=db,
            thread_id=thread_id,
            tenant_id=tenant_id,
            request=request
        )

        if not thread:
            raise HTTPException(status_code=404, detail="Thread not found")

        return {
            "success": True,
            "message": "Thread updated successfully",
            "thread_id": str(thread.id),
            "new_title": thread.title
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating thread {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))