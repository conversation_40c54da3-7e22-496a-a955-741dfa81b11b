#!/usr/bin/env python3
"""
Final Results Checker

Check if proposals were successfully generated and moved to review/format queues.
"""

import asyncio
from database import get_customer_db
from controllers.customer.proposals_in_review_controller import ProposalsInReviewController
from controllers.customer.proposals_format_queue_controller import ProposalsFormatQueueController

async def check_final():
    opportunity_id = "vSe1unlCj9"
    
    print(f"🔍 Checking final results for opportunity: {opportunity_id}")
    print("=" * 60)
    
    try:
        async for db in get_customer_db():
            # Check proposals in review
            print("📋 PROPOSALS IN REVIEW:")
            review_items = await ProposalsInReviewController.get_by_opportunity_id(db, opportunity_id)
            
            if review_items:
                print(f"  ✅ Found {len(review_items)} proposals in review")
                volumes = {}
                for item in review_items:
                    vol = item.volume_number
                    if vol not in volumes:
                        volumes[vol] = []
                    volumes[vol].append(item.section_number)
                    print(f"    - Volume {item.volume_number}, Section {item.section_number} (Status: {item.status})")
                
                print(f"  📊 Summary: {len(volumes)} volumes with sections")
                for vol, sections in volumes.items():
                    print(f"    Volume {vol}: {len(sections)} sections")
            else:
                print("  ❌ No proposals found in review")
            
            # Check proposals in format queue
            print("\n📄 PROPOSALS IN FORMAT QUEUE:")
            format_items = await ProposalsFormatQueueController.get_by_opportunity_id(db, opportunity_id)
            
            if format_items:
                print(f"  ✅ Found {len(format_items)} proposals in format queue")
                for item in format_items:
                    print(f"    - Version {item.version} (Status: {item.status}, Type: {item.format_type})")
            else:
                print("  ❌ No proposals found in format queue")
            
            # Overall status
            print("\n🎯 FINAL STATUS:")
            has_review = len(review_items) > 0 if review_items else False
            has_format = len(format_items) > 0 if format_items else False
            
            if has_review or has_format:
                print("  🎉 SUCCESS: Proposals were generated and moved to queues!")
                if has_review:
                    print("    ✅ Proposals available for review")
                if has_format:
                    print("    ✅ Proposals available for formatting")
            else:
                print("  ⏳ No proposals found in final queues yet")
                print("    This could mean:")
                print("    - Proposal generation is still in progress")
                print("    - Proposal scheduler hasn't run yet")
                print("    - There was an error in the proposal generation")
            
            break
        
    except Exception as e:
        print(f"❌ Error checking final results: {e}")

if __name__ == "__main__":
    asyncio.run(check_final())
