#!/bin/bash

# Simple monitoring script for scheduler flow
# Usage: ./monitor_flow.sh

OPPORTUNITY_ID="vSe1unlCj9"
BASE_URL="http://localhost:8000"

echo "🔍 Monitoring Scheduler Flow for Opportunity: $OPPORTUNITY_ID"
echo "============================================================"

while true; do
    echo ""
    echo "=== $(date) ==="
    
    # Check Custom Opps Queue
    echo "📋 Custom Opps Queue Status:"
    curl -s "$BASE_URL/queue/custom-opps/new?limit=5" | jq --arg id "$OPPORTUNITY_ID" '.items[] | select(.opps_id==$id) | {status, created_date, update_date}' 2>/dev/null || echo "  No items or API error"
    
    # Check Proposal Queue
    echo "📄 Proposal Queue Status:"
    curl -s "$BASE_URL/queue/proposal/new?limit=5" | jq --arg id "$OPPORTUNITY_ID" '.items[] | select(.opps_id==$id) | {status, creation_date, next_state}' 2>/dev/null || echo "  No items or API error"
    
    # Check Database Results
    echo "💾 Database Check:"
    python3 check_db_results.py 2>/dev/null | grep -E "(✅|❌|⏳|🎉)" || echo "  Database check failed"
    
    # Check Final Results
    echo "🎯 Final Results:"
    python3 check_final_results.py 2>/dev/null | grep -E "(✅|❌|⏳|🎉)" || echo "  Final check failed"
    
    echo "⏰ Sleeping 20 seconds... (Press Ctrl+C to stop)"
    sleep 20
done
