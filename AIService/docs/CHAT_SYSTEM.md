# Enhanced Chat System

A ChatGPT-like chat system with thread management, memory, and streaming responses.

## Features

- **Thread Management**: Create, list, update, and delete chat threads
- **Streaming Responses**: All responses are streamed for real-time interaction
- **Memory Management**: Automatic conversation history trimming and summarization
- **Context Integration**: Uses ChromaDB for relevant document context
- **Web Search**: Real-time web search using Gemini with Google Search
- **Multi-Provider LLM Support**: Works with any AI provider (Ollama, Gemini, etc.)

## API Endpoints

### Chat Endpoints

#### POST /chats/ask
Stream a chat response. Creates a new thread or uses existing thread.

**Request Body:**
```json
{
  "message": "Your question here",
  "thread_id": "optional-existing-thread-id",
  "opportunity_id": "required-opportunity-id",
  "tenant_id": "required-tenant-id",
  "source": "required-source-type",
  "max_chunks": 5,
  "web_search": false
}
```

**Response:** Streaming text response

### Thread Management Endpoints

#### GET /chats/threads
List chat threads for a tenant.

**Query Parameters:**
- `tenant_id` (required)
- `opportunity_id` (optional)
- `source` (optional)
- `limit` (default: 50)
- `offset` (default: 0)
- `include_archived` (default: false)

#### GET /chats/threads/{thread_id}
Get a specific thread with its message history.

**Query Parameters:**
- `tenant_id` (required)

#### DELETE /chats/threads/{thread_id}
Delete a thread and all its messages.

**Query Parameters:**
- `tenant_id` (required)

#### PUT /chats/threads/{thread_id}
Update thread properties.

**Query Parameters:**
- `tenant_id` (required)
- `title` (optional)
- `is_archived` (optional)

## Database Schema

### chat_threads
- `id` (UUID, Primary Key)
- `tenant_id` (VARCHAR, Required)
- `title` (VARCHAR, Required)
- `opportunity_id` (VARCHAR, Optional)
- `source` (VARCHAR, Optional)
- `created_date` (TIMESTAMP)
- `last_activity_date` (TIMESTAMP)
- `is_archived` (BOOLEAN)
- `message_count` (INTEGER)
- `summary` (TEXT)
- `metadata` (JSONB)

### chat_messages
- `id` (UUID, Primary Key)
- `thread_id` (UUID, References chat_threads.id)
- `role` (VARCHAR: 'user', 'assistant', 'system')
- `content` (TEXT)
- `created_date` (TIMESTAMP)
- `token_count` (INTEGER)
- `context_chunks` (JSONB)
- `metadata` (JSONB)

## Memory Management

The system automatically manages conversation history:

1. **Trimming**: When conversations exceed 20 messages, older messages are summarized
2. **Summarization**: Uses LLM to create concise summaries of conversation history
3. **Context Preservation**: Always keeps the most recent 10 messages for immediate context
4. **ChromaDB Integration**: Retrieves relevant document context for each query

## Configuration

The system uses the existing LLM factory configuration:
- Supports Ollama and Gemini providers
- Configurable through environment variables
- Temperature and other parameters can be adjusted

### Web Search Configuration

To enable web search capabilities, set the following environment variable:
```bash
GEMINI_API_KEY=your_gemini_api_key_here
```

Web search features:
- Uses Gemini 2.0 Flash with Google Search tool
- Combines document context with real-time web results
- Cites sources and provides up-to-date information
- Automatically enabled when `web_search: true` in requests

## Usage Examples

### Start a new chat
```bash
curl -X POST "http://localhost:8000/chats/ask" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What are the key requirements for this RFP?",
    "opportunity_id": "RFP-2024-001",
    "tenant_id": "acme-corp",
    "source": "custom"
  }'
```

### Start a chat with web search
```bash
curl -X POST "http://localhost:8000/chats/ask" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What are the latest trends in government AI procurement?",
    "opportunity_id": "RFP-2024-001",
    "tenant_id": "acme-corp",
    "source": "custom",
    "web_search": true
  }'
```

### Continue existing chat
```bash
curl -X POST "http://localhost:8000/chats/ask" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Can you provide more details about the technical requirements?",
    "thread_id": "existing-thread-uuid",
    "opportunity_id": "RFP-2024-001", 
    "tenant_id": "acme-corp",
    "source": "custom"
  }'
```

### List threads
```bash
curl "http://localhost:8000/chats/threads?tenant_id=acme-corp&opportunity_id=RFP-2024-001"
```

## Migration

Run the migration script to add the required tables:

```sql
-- See migrations/add_chat_tables.sql
```

## Architecture

- **EnhancedChatService**: Main service handling chat logic
- **ThreadService**: Database operations for threads and messages
- **MemoryService**: Conversation history management and summarization
- **ChromaService**: Document context retrieval
- **LLMFactory**: Multi-provider LLM abstraction

The system is designed to be modular, scalable, and provider-agnostic.
