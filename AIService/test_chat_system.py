#!/usr/bin/env python3
"""
Test script for the enhanced chat system
"""
import asyncio
import json
from sqlalchemy.ext.asyncio import AsyncSession
from database import get_customer_db
from services.chat.enhanced_chat_service import EnhancedChatService
from services.chat.web_search_service import WebSearchService
from schemas.chat_schemas import ChatR<PERSON>quest


async def test_basic_chat():
    """Test basic chat functionality"""
    print("🧪 Testing basic chat functionality...")
    
    chat_service = EnhancedChatService()
    
    # Create a test request
    request = ChatRequest(
        message="Hello, can you help me understand government contracting?",
        opportunity_id="TEST-001",
        tenant_id="test-tenant",
        source="custom",
        max_chunks=3,
        web_search=False
    )
    
    try:
        async for db in get_customer_db():
            print("📝 Sending test message...")
            
            response_chunks = []
            async for chunk in chat_service.chat(
                db=db,
                request=request,
                tenant_id="test-tenant",
                user_id=1
            ):
                response_chunks.append(chunk)
                print(f"📤 Chunk: {chunk[:50]}...")
            
            full_response = "".join(response_chunks)
            print(f"✅ Basic chat test completed. Response length: {len(full_response)} characters")
            break
            
    except Exception as e:
        print(f"❌ Basic chat test failed: {e}")


async def test_web_search():
    """Test web search functionality"""
    print("\n🌐 Testing web search functionality...")
    
    web_search_service = WebSearchService()
    
    if not web_search_service.is_available():
        print("⚠️  Web search not available - GEMINI_API_KEY not configured")
        return
    
    try:
        print("🔍 Testing web search connection...")
        connection_ok = await web_search_service.test_connection()
        
        if connection_ok:
            print("✅ Web search connection successful")
            
            print("🔍 Performing test web search...")
            response_chunks = []
            async for chunk in web_search_service.search_and_stream(
                query="What are the latest government AI procurement guidelines?",
                context_chunks=["Test context about government contracting"],
                conversation_history="User: Hello\nAssistant: How can I help you?"
            ):
                response_chunks.append(chunk)
                print(f"📤 Search chunk: {chunk[:50]}...")
            
            full_response = "".join(response_chunks)
            print(f"✅ Web search test completed. Response length: {len(full_response)} characters")
        else:
            print("❌ Web search connection failed")
            
    except Exception as e:
        print(f"❌ Web search test failed: {e}")


async def test_chat_with_web_search():
    """Test chat with web search enabled"""
    print("\n🌐💬 Testing chat with web search...")
    
    chat_service = EnhancedChatService()
    
    if not chat_service.web_search_service.is_available():
        print("⚠️  Web search not available - skipping test")
        return
    
    # Create a test request with web search enabled
    request = ChatRequest(
        message="What are the current trends in government cybersecurity requirements?",
        opportunity_id="TEST-002",
        tenant_id="test-tenant",
        source="custom",
        max_chunks=3,
        web_search=True
    )
    
    try:
        async for db in get_customer_db():
            print("📝 Sending test message with web search...")
            
            response_chunks = []
            async for chunk in chat_service.chat(
                db=db,
                request=request,
                tenant_id="test-tenant",
                user_id=1
            ):
                response_chunks.append(chunk)
                print(f"📤 Web search chunk: {chunk[:50]}...")
            
            full_response = "".join(response_chunks)
            print(f"✅ Chat with web search test completed. Response length: {len(full_response)} characters")
            break
            
    except Exception as e:
        print(f"❌ Chat with web search test failed: {e}")


async def test_thread_management():
    """Test thread management functionality"""
    print("\n📋 Testing thread management...")
    
    from services.chat.thread_service import ThreadService
    from schemas.chat_schemas import ThreadCreateRequest
    
    try:
        async for db in get_customer_db():
            print("📝 Creating test thread...")
            
            # Create a thread
            thread_request = ThreadCreateRequest(
                title="Test Thread",
                opportunity_id="TEST-003",
                tenant_id="test-tenant",
                source="custom"
            )
            
            thread = await ThreadService.create_thread(db, thread_request)
            print(f"✅ Thread created with ID: {thread.id}")
            
            # Add a message
            message = await ThreadService.add_message(
                db=db,
                thread_id=thread.id,
                role="user",
                content="Test message",
                tenant_id="test-tenant"
            )
            print(f"✅ Message added with ID: {message.id}")
            
            # Get thread with messages
            retrieved_thread = await ThreadService.get_thread(
                db=db,
                thread_id=thread.id,
                tenant_id="test-tenant"
            )
            print(f"✅ Thread retrieved: {retrieved_thread.title}")
            
            # Clean up - delete the test thread
            deleted = await ThreadService.delete_thread(
                db=db,
                thread_id=thread.id,
                tenant_id="test-tenant"
            )
            print(f"✅ Thread deleted: {deleted}")
            break
            
    except Exception as e:
        print(f"❌ Thread management test failed: {e}")


async def main():
    """Run all tests"""
    print("🚀 Starting Enhanced Chat System Tests\n")
    
    await test_basic_chat()
    await test_web_search()
    await test_chat_with_web_search()
    await test_thread_management()
    
    print("\n🎉 All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
