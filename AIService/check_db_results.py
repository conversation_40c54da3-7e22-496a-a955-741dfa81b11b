#!/usr/bin/env python3
"""
Simple Database Results Checker

Quick script to check if the scheduler flow is working by examining the database.
"""

import asyncio
import json
from database import get_customer_db
from controllers.customer.custom_opps_controller import CustomOpportunitiesController

async def check_results():
    opportunity_id = "vSe1unlCj9"
    
    print(f"🔍 Checking results for opportunity: {opportunity_id}")
    print("=" * 60)
    
    try:
        async for db in get_customer_db():
            record = await CustomOpportunitiesController.get_by_opportunity_id(db, opportunity_id)
            break
        
        if not record:
            print("❌ No record found in database")
            return
        
        print(f"✅ Found record: {record.title}")
        print(f"📝 Description: {record.description[:100]}..." if record.description else "No description")
        
        # Check compliance data
        print("\n📋 COMPLIANCE DATA:")
        content_ok = bool(record.content_compliance and str(record.content_compliance).strip())
        structure_ok = bool(record.structure_compliance and str(record.structure_compliance).strip())
        
        print(f"  Content compliance: {'✅' if content_ok else '❌'}")
        if content_ok:
            print(f"    Length: {len(str(record.content_compliance))} characters")
        
        print(f"  Structure compliance: {'✅' if structure_ok else '❌'}")
        if structure_ok:
            print(f"    Length: {len(str(record.structure_compliance))} characters")
        
        # Check TOCs
        print("\n📑 TABLE OF CONTENTS:")
        toc_count = 0
        toc_details = []
        
        for i in range(1, 6):
            field = "toc_text" if i == 1 else f"toc_text_{i}"
            if hasattr(record, field):
                toc_value = getattr(record, field)
                if toc_value and str(toc_value).strip():
                    toc_count += 1
                    try:
                        toc_json = json.loads(str(toc_value))
                        sections = len(toc_json) if isinstance(toc_json, list) else 1
                        toc_details.append(f"Volume {i}: {sections} sections")
                        print(f"  ✅ Volume {i}: {sections} sections")
                    except:
                        toc_details.append(f"Volume {i}: Invalid JSON")
                        print(f"  ⚠️ Volume {i}: Invalid JSON")
                else:
                    print(f"  ❌ Volume {i}: Not generated")
        
        print(f"  Total TOCs: {toc_count}/5")
        
        # Check outlines
        print("\n📋 OUTLINES:")
        outline_count = 0
        outline_details = []
        
        for i in range(1, 6):
            field = f"proposal_outline_{i}"
            if hasattr(record, field):
                outline_value = getattr(record, field)
                if outline_value and str(outline_value).strip():
                    outline_count += 1
                    try:
                        outline_json = json.loads(str(outline_value))
                        if "outlines" in outline_json:
                            sections = len(outline_json["outlines"])
                            outline_details.append(f"Volume {i}: {sections} sections")
                            print(f"  ✅ Volume {i}: {sections} sections")
                        else:
                            outline_details.append(f"Volume {i}: No 'outlines' key")
                            print(f"  ⚠️ Volume {i}: No 'outlines' key")
                    except:
                        outline_details.append(f"Volume {i}: Invalid JSON")
                        print(f"  ⚠️ Volume {i}: Invalid JSON")
                else:
                    print(f"  ❌ Volume {i}: Not generated")
        
        print(f"  Total Outlines: {outline_count}/5")
        
        # Summary
        print("\n🎯 SUMMARY:")
        step1 = content_ok and structure_ok
        step2 = toc_count > 0
        step3 = outline_count > 0
        
        print(f"  Step 1 - Compliance Generation: {'✅' if step1 else '❌'}")
        print(f"  Step 2 - TOC Generation: {'✅' if step2 else '❌'}")
        print(f"  Step 3 - Outline Generation: {'✅' if step3 else '❌'}")
        
        if step1 and step2 and step3:
            print("  🎉 All steps completed successfully!")
        elif step1 and step2:
            print("  ⏳ Ready for outline generation")
        elif step1:
            print("  ⏳ Ready for TOC generation")
        else:
            print("  ⏳ Waiting for compliance generation")
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")

if __name__ == "__main__":
    asyncio.run(check_results())
